/* 悬浮UI样式 - 使用命名空间避免与网页样式冲突 */
.approval-floating-ui {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    width: 350px !important;
    max-height: 80vh !important;
    background: #ffffff !important;
    border: 2px solid #007bff !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
    z-index: 2147483647 !important; /* 最高层级 */
    color: #333 !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
}

.approval-floating-ui.minimized {
    height: auto !important;
    max-height: none !important;
}

.approval-floating-ui * {
    box-sizing: border-box !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* 头部 */
.floating-ui-header {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
    color: white !important;
    padding: 12px 16px !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    cursor: grab !important;
    user-select: none !important;
}

.floating-ui-header:active {
    cursor: grabbing !important;
}

.floating-ui-title {
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
    font-weight: 600 !important;
    font-size: 16px !important;
}

.floating-ui-status {
    background: rgba(255, 255, 255, 0.2) !important;
    padding: 4px 8px !important;
    border-radius: 12px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
}

.floating-ui-status.status-running {
    background: #28a745 !important;
    animation: pulse 2s infinite !important;
}

.floating-ui-status.status-paused {
    background: #ffc107 !important;
    color: #333 !important;
}

.floating-ui-status.status-waiting,
.floating-ui-status.status-waiting-user {
    background: #fd7e14 !important;
    animation: blink 1.5s infinite !important;
}

.floating-ui-status.status-completed {
    background: #28a745 !important;
}

.floating-ui-status.status-idle {
    background: #6c757d !important;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.floating-ui-controls {
    display: flex !important;
    gap: 8px !important;
}

.floating-btn-icon {
    background: rgba(255, 255, 255, 0.2) !important;
    border: none !important;
    color: white !important;
    width: 28px !important;
    height: 28px !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    font-size: 16px !important;
    font-weight: bold !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: background-color 0.2s !important;
}

.floating-btn-icon:hover {
    background: rgba(255, 255, 255, 0.3) !important;
}

/* 内容区域 */
.floating-ui-content {
    max-height: calc(80vh - 60px) !important;
    overflow-y: auto !important;
    padding: 16px !important;
}

.floating-section {
    margin-bottom: 16px !important;
    border: 1px solid #e9ecef !important;
    border-radius: 6px !important;
    overflow: hidden !important;
}

.floating-section-header {
    background: #f8f9fa !important;
    padding: 10px 12px !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    color: #495057 !important;
    border-bottom: 1px solid #e9ecef !important;
}

.floating-section-header svg {
    flex-shrink: 0 !important;
}

/* 变量表格 */
.floating-table {
    width: 100% !important;
    border-collapse: collapse !important;
    font-size: 12px !important;
}

.floating-table th,
.floating-table td {
    padding: 8px 10px !important;
    text-align: left !important;
    border-bottom: 1px solid #e9ecef !important;
    word-break: break-word !important;
}

.floating-table th {
    background: #f8f9fa !important;
    font-weight: 600 !important;
    color: #495057 !important;
    font-size: 11px !important;
    text-transform: uppercase !important;
}

.floating-table td:last-child {
    max-width: 120px !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

/* 警告样式 */
.floating-warning {
    border-color: #ffc107 !important;
    background: #fff3cd !important;
}

.floating-warning .floating-section-header {
    background: #ffeaa7 !important;
    color: #856404 !important;
}

.floating-list {
    list-style: none !important;
    padding: 12px !important;
}

.floating-list li {
    padding: 8px 0 !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
}

.floating-list li:last-child {
    border-bottom: none !important;
}

.floating-warning-message {
    font-weight: 600 !important;
    margin-bottom: 4px !important;
    color: #856404 !important;
}

.floating-warning-details {
    font-size: 11px !important;
    color: #6c757d !important;
    background: rgba(255, 255, 255, 0.7) !important;
    padding: 6px 8px !important;
    border-radius: 4px !important;
    white-space: pre-wrap !important;
}

/* 错误样式 */
.floating-error {
    border-color: #dc3545 !important;
    background: #f8d7da !important;
}

.floating-error .floating-section-header {
    background: #f5c6cb !important;
    color: #721c24 !important;
}

.floating-error-message {
    padding: 12px !important;
    font-weight: 600 !important;
    color: #721c24 !important;
}

.floating-error-details {
    padding: 0 12px 12px !important;
    font-size: 11px !important;
    color: #6c757d !important;
    background: rgba(255, 255, 255, 0.7) !important;
    margin: 0 12px 12px !important;
    padding: 8px !important;
    border-radius: 4px !important;
    white-space: pre-wrap !important;
}

/* 成功样式 */
.floating-success {
    border-color: #28a745 !important;
    background: #d4edda !important;
}

.floating-success .floating-section-header {
    background: #c3e6cb !important;
    color: #155724 !important;
}

.floating-completion-content {
    padding: 12px !important;
    color: #155724 !important;
    white-space: pre-wrap !important;
}

/* 任务列表 */
.floating-task-list {
    max-height: 200px !important;
    overflow-y: auto !important;
    padding: 8px !important;
}

.floating-task-items {
    list-style: none !important;
}

.floating-task-item {
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
    padding: 8px 10px !important;
    margin-bottom: 4px !important;
    border-radius: 4px !important;
    font-size: 12px !important;
    transition: background-color 0.2s !important;
}

.floating-task-item.completed {
    color: #6c757d !important;
    text-decoration: line-through !important;
    background: #f8f9fa !important;
}

.floating-task-item.running {
    background: #e3f2fd !important;
    font-weight: 600 !important;
    border: 1px solid #2196f3 !important;
}

.floating-task-item.waiting {
    background: #fff3e0 !important;
    font-weight: 600 !important;
    border: 1px dashed #ff9800 !important;
}

.floating-task-item.paused {
    background: #fff3e0 !important;
    border: 1px solid #ffc107 !important;
}

.floating-task-icon {
    flex-shrink: 0 !important;
    width: 16px !important;
    height: 16px !important;
}

.floating-task-description {
    flex: 1 !important;
    word-break: break-word !important;
}

.floating-no-tasks {
    padding: 20px !important;
    text-align: center !important;
    color: #6c757d !important;
    font-style: italic !important;
}

/* 控制按钮 */
.floating-controls {
    display: flex !important;
    gap: 8px !important;
    flex-wrap: wrap !important;
    margin-top: 16px !important;
    padding-top: 16px !important;
    border-top: 1px solid #e9ecef !important;
}

.floating-btn {
    flex: 1 !important;
    min-width: 70px !important;
    padding: 8px 12px !important;
    border: 1px solid #ced4da !important;
    border-radius: 4px !important;
    background: #ffffff !important;
    color: #495057 !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.2s !important;
}

.floating-btn:hover:not(:disabled) {
    background: #f8f9fa !important;
    border-color: #adb5bd !important;
}

.floating-btn:disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    background: #e9ecef !important;
}

.floating-btn-primary {
    background: #007bff !important;
    color: white !important;
    border-color: #007bff !important;
}

.floating-btn-primary:hover:not(:disabled) {
    background: #0056b3 !important;
    border-color: #0056b3 !important;
}

/* 滚动条样式 */
.floating-ui-content::-webkit-scrollbar,
.floating-task-list::-webkit-scrollbar {
    width: 6px !important;
}

.floating-ui-content::-webkit-scrollbar-track,
.floating-task-list::-webkit-scrollbar-track {
    background: #f1f1f1 !important;
    border-radius: 3px !important;
}

.floating-ui-content::-webkit-scrollbar-thumb,
.floating-task-list::-webkit-scrollbar-thumb {
    background: #c1c1c1 !important;
    border-radius: 3px !important;
}

.floating-ui-content::-webkit-scrollbar-thumb:hover,
.floating-task-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8 !important;
}
