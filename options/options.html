﻿<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" href="options.css">
</head>
<body>
    <h1>脚本配置</h1>
    <p>请在此处定义您的审批自动化脚本（JSON格式）</p>
    <textarea id="scriptEditor" rows="20" cols="80"></textarea>
    
    <details>
        <summary><strong>点击查看指令说明</strong></summary>
        <div class="instructions">
            <h3>核心概念</h3>
            <div class="concept">
                <h4>类型安全的变量</h4>
                <p>所有变量都包含一个值和一个类型（<code>string</code> 或 <code>number</code>）。这种设计从根本上避免了因类型混淆（例如将 "10" 和 1 相加得到 "101"）而导致的错误。您必须通过专门的指令来创建和转换变量。</p>
            </div>
            <div class="concept">
                <h4>标签 (label)</h4>
                <p>您可以为任何步骤添加一个 <code>"label"</code> 属性，作为跳转指令的目标。标签在整个脚本中必须是唯一的，它本身不执行任何操作。</p>
                <pre>{ "label": "loop_start", "description": "这是一个循环的起点" }</pre>
            </div>
            <div class="concept">
                <h4>步骤描述 (description)</h4>
                <p>这是一个强烈推荐添加到每个指令中的可选参数。它的内容会显示在插件的弹出窗口中，作为该步骤的人类可读的说明。这对于调试和理解脚本流程至关重要。</p>
                <pre>{ "command": "click", "selector": "#submit", "description": "第3步：点击提交按钮" }</pre>
            </div>
            <div class="concept">
                <h4>全局变量支持</h4>
                <p>现在，<strong>所有指令的所有参数</strong>，只要其值为字符串格式，都统一支持使用 <code>${variableName}</code> 语法来插入变量。这包括嵌套对象内部的字段，例如高级选择器中的 <code>by</code>, <code>value</code>, 和 <code>index</code>。</p>
                <pre>{ "command": "click", "selector": { "by": "${attr_name}", "value": "${attr_value}", "index": "${item_index}" } }</pre>
            </div>

            <hr>

            <h3>变量管理指令</h3>
            <ul>
                <li>
                    <strong><code>init_variable</code> (初始化变量)</strong>
                    <p><strong>作用:</strong> 创建一个新变量，并明确其类型和初始值。这是定义一个变量的起点。</p>
                    <p><strong>参数:</strong></p>
                    <ul>
                        <li><code>variableName</code> (必需): 您要创建的变量名。</li>
                        <li><code>type</code> (必需): 变量的类型，必须是 <code>"string"</code> 或 <code>"number"</code>。</li>
                        <li><code>value</code> (必需): 变量的初始值。如果类型是number，此值必须能被解析为数字。</li>
                    </ul>
                    <pre>{ "command": "init_variable", "variableName": "max_retries", "type": "number", "value": 5 }</pre>
                    <pre>{ "command": "init_variable", "variableName": "status", "type": "string", "value": "pending" }</pre>
                </li>
                <li>
                    <strong><code>scrape_text</code> (抓取文本)</strong>
                    <p><strong>作用:</strong> 从页面上的一个元素抓取其可见文本，并总是将其保存为一个 <strong>string</strong> 类型的变量。如果目标变量已存在，它将被覆盖。</p>
                    <p><strong>参数:</strong></p>
                    <ul>
                        <li><code>selector</code> (必需): 用于定位元素的CSS选择器或XPath。</li>
                        <li><code>outputVariable</code> (必需): 用于存储结果的变量名。</li>
                    </ul>
                    <pre>{ "command": "scrape_text", "selector": "#username-display", "outputVariable": "username" }</pre>
                </li>
                <li>
                    <strong><code>scrape_html</code> (抓取HTML)</strong>
                    <p><strong>作用:</strong> 从页面上的一个元素抓取其外部HTML (outerHTML)，或在未提供选择器时抓取整个页面的HTML，并将其保存为一个 <strong>string</strong> 类型的变量。</p>
                    <p><strong>参数:</strong></p>
                    <ul>
                        <li><code>selector</code> (可选): 用于定位元素的CSS选择器或XPath。如果省略，则抓取整个文档。</li>
                        <li><code>outputVariable</code> (必需): 用于存储结果的变量名。</li>
                    </ul>
                    <pre>{ "command": "scrape_html", "selector": "#main-content", "outputVariable": "main_html" }</pre>
                    <pre>{ "command": "scrape_html", "outputVariable": "full_page_html" }</pre>
                </li>
                <li>
                    <strong><code>parse_number</code> (解析数字)</strong>
                    <p><strong>作用:</strong> 将一个 <strong>string</strong> 类型的变量转换为 <strong>number</strong> 类型，并存入一个新变量。这是进行数学计算前的关键类型转换步骤。</p>
                    <p><strong>参数:</strong></p>
                    <ul>
                        <li><code>inputVariable</code> (必需): 源变量名，其类型必须是 <code>string</code>。</li>
                        <li><code>outputVariable</code> (必需): 用于存储数字结果的新变量名。</li>
                    </ul>
                    <p class="notice"><strong>注意:</strong> 如果源字符串无法被解析为一个有效的数字（例如 "hello" 或 "1,200"），脚本将报错并停止。</p>
                    <pre>{ "command": "parse_number", "inputVariable": "price_text", "outputVariable": "price_num" }</pre>
                </li>
                <li>
                    <strong><code>calculate</code> (数学计算)</strong>
                    <p><strong>作用:</strong> 执行一个简单的二元数学运算，并将结果存入一个 <code>number</code> 类型的变量。</p>
                    <p><strong>参数:</strong></p>
                    <ul>
                        <li><code>expression</code> (必需): 一个简单的数学表达式字符串。</li>
                        <li><code>outputVariable</code> (必需): 用于存储计算结果的变量名。</li>
                    </ul>
                    <p class="notice"><strong>注意：</strong> <code>expression</code> 仅支持两个操作数和一个运算符（<code>+</code>, <code>-</code>, <code>*</code>, <code>/</code>），不支持括号或连续运算。</p>
                    <p><strong>示例:</strong></p>
                    <pre>{ "command": "calculate", "expression": "price * 1.05", "outputVariable": "total_price" }</pre>
                    <pre>{ "command": "calculate", "expression": "count + 1", "outputVariable": "count" }</pre>
                </li>
                <li>
                    <strong><code>format_string</code> (格式化字符串)</strong>
                    <p><strong>作用:</strong> 使用一个模板和现有变量的值来创建一个新的 <strong>string</strong> 类型的变量。</p>
                    <p><strong>参数:</strong></p>
                    <ul>
                        <li><code>template</code> (必需): 包含占位符的字符串模板。使用 <code>${variableName}</code> 语法来引用变量。</li>
                        <li><code>outputVariable</code> (必需): 用于存储最终字符串的变量名。</li>
                    </ul>
                    <pre>{ "command": "format_string", "template": "你好, ${username}！你的订单总额是 ${total_cost}。", "outputVariable": "greeting_message" }</pre>
                </li>
            </ul>

            <hr>

            <h3>流程控制与页面交互</h3>
            <ul>
                <li>
                    <strong><code>conditional_jump</code> (条件跳转)</strong>
                    <p><strong>作用:</strong> 当一个简单的比较条件为真时，跳转到指定的标签。</p>
                    <p><strong>参数:</strong></p>
                    <ul>
                        <li><code>condition</code> (必需): 一个简单的比较表达式。</li>
                        <li><code>targetLabel</code> (必需): 要跳转到的目标标签名。</li>
                    </ul>
                    <p class="notice"><strong>注意：</strong> <code>condition</code> 仅支持两个操作数和一个比较运算符（<code>==</code>, <code>!=</code>, <code>===</code>, <code>!==</code>, <code>></code>, <code><</code>, <code>>=</code>, <code><=</code>），不支持逻辑与(<code>&&</code>)或逻辑或(<code>||</code>)。如需实现复杂逻辑，请使用多个连续的跳转指令。</p>
                    <p><strong>示例:</strong></p>
                    <pre>{ "command": "conditional_jump", "condition": "retry_count < 5", "targetLabel": "retry_block" }</pre>
                    <pre>{ "command": "conditional_jump", "condition": "status === 'completed'", "targetLabel": "finish_script" }</pre>
                </li>
                <li>
                    <strong><code>jump</code> (无条件跳转)</strong>
                    <p><strong>作用:</strong> 无条件地跳转到指定的标签，常用于构建循环的结尾。</p>
                    <pre>{ "command": "jump", "targetLabel": "loop_start" }</pre>
                </li>
                <li>
                    <strong><code>goto</code> (跳转页面)</strong>
                    <p><strong>作用:</strong> 导航到指定的URL。</p>
                    <p><strong>参数:</strong></p>
                    <ul>
                        <li><code>url</code> (必需): 目标URL。支持使用 <code>${variableName}</code> 语法。</li>
                        <li><code>newTab</code> (可选): 布尔值 (<code>true</code> 或 <code>false</code>)。如果为 <code>true</code>，将在新标签页中打开URL并自动切换过去。默认为 <code>false</code>。</li>
                    </ul>
                    <pre>{ "command": "goto", "url": "https://example.com/profiles/${userId}", "newTab": true }</pre>
                </li>
                <li>
                    <strong><code>switch_tab</code> (切换标签页)</strong>
                    <p><strong>作用:</strong> 根据URL或页面标题查找一个已打开的标签页，并将其切换为当前活动标签页。</p>
                    <p><strong>参数:</strong></p>
                    <ul>
                        <li><code>url_pattern</code> (可选): 用于匹配标签页URL的匹配模式。例如 <code>"*://*.baidu.com/*"</code>。</li>
                        <li><code>title_pattern</code> (可选): 用于匹配页面标题的字符串，支持使用星号(<code>*</code>)作为通配符。例如 <code>"*搜索结果"</code>。</li>
                        <li><code>closePreviousTab</code> (可选): 一个布尔值 (<code>true</code> 或 <code>false</code>)。如果设置为 <code>true</code>，在成功切换到新标签页后，将关闭之前的标签页。默认为 <code>false</code>。</li>
                    </ul>
                    <p class="notice"><strong>注意:</strong> 必须提供 <code>url_pattern</code> 和 <code>title_pattern</code> 中的至少一个。如果找到多个匹配项，将激活第一个。如果找不到，脚本将报错。</p>
                    <pre>{ "command": "switch_tab", "url_pattern": "*://baidu.com/*" }</pre>
                    <pre>{ "command": "switch_tab", "title_pattern": "*搜索结果" }</pre>
                </li>
                <li>
                    <strong><code>switch_iframe</code> (切换IFrame)</strong>
                    <p><strong>作用:</strong> 将后续所有元素查找和操作的上下文切换到一个指定的同源 <code>iframe</code> 内部。也可以用于从 iframe 切回到主文档。</p>
                    <p><strong>参数:</strong></p>
                    <ul>
                        <li><code>selector</code> (可选): 用于定位目标 <code>iframe</code> 元素的选择器。如果省略此参数，上下文将切换回页面的顶层主文档。</li>
                    </ul>
                    <p class="notice"><strong>注意:</strong> 此指令只能用于切换到与主页面同源的 iframe。尝试切换到跨域的 iframe 将会导致错误。</p>
                    <p><strong>示例:</strong></p>
                    <pre>{ "command": "switch_iframe", "selector": "#editor-iframe", "description": "切换到编辑器iframe" }</pre>
                    <pre>{ "command": "switch_iframe", "description": "切换回主文档" }</pre>
                </li>
                <li>
                    <strong><code>click</code> (点击元素)</strong>
                    <p><strong>作用:</strong> 查找并点击一个页面元素，可指定点击次数。</p>
                    <p><strong>参数:</strong></p>
                    <ul>
                        <li><code>selector</code> (必需): 目标元素的选择器。</li>
                        <li><code>clicks</code> (可选): 点击次数。默认为 <code>1</code>。<strong>特别地，当 <code>clicks</code> 设置为 <code>2</code> 时，会触发一个原生的 <code>dblclick</code> (双击) 事件。</strong>对于其他次数，则为多次独立的单击。支持使用 <code>${variableName}</code> 语法。</li>
                        <li><code>clickDelay</code> (可选): 每次点击之间的延迟（毫秒）。默认为 <code>100</code>。仅在 <code>clicks</code> > 1 且不等于 2 时生效。支持使用 <code>${variableName}</code> 语法。</li>
                    </ul>
                    <pre>{ "command": "click", "selector": "#like-button", "clicks": 2, "clickDelay": 500 }</pre>
                </li>
                <li>
                    <strong><code>type</code> (输入文本)</strong>
                    <p><strong>作用:</strong> 向指定的元素输入文本。<code>selector</code> 和 <code>value</code> 参数都支持使用 <code>${variableName}</code> 语法。</p>
                    <pre>{ "command": "type", "selector": "#message-box", "value": "${greeting_message}" }</pre>
                </li>
                <li>
                    <strong><code>wait</code> (等待)</strong>
                    <p><strong>作用:</strong> 暂停脚本执行指定的毫秒数。<code>milliseconds</code> 参数支持使用 <code>${variableName}</code> 语法（会被自动转换为数字）。</p>
                    <pre>{ "command": "wait", "milliseconds": "${delay_time}" }</pre>
                </li>
                <li>
                    <strong><code>wait_for_user</code> (等待用户指令)</strong>
                    <p><strong>作用:</strong> 暂停脚本执行，直到用户在插件弹窗中手动点击“下一步”按钮。这对于需要人工检查或干预的步骤非常有用。</p>
                    <p><strong>参数:</strong> 无。</p>
                    <pre>{ "command": "wait_for_user", "description": "等待人工确认页面内容" }</pre>
                </li>
                <li>
                    <strong><code>api_request</code> (API 请求)</strong>
                    <p><strong>作用:</strong> 发起一个网络请求，解析返回的JSON，并将指定的字段存入多个变量中。</p>
                    <p><strong>参数:</strong></p>
                    <ul>
                        <li><code>url</code> (必需): 请求的目标URL。</li>
                        <li><code>outputs</code> (必需): 一个对象，用于定义如何从JSON响应中提取数据。
                            <ul>
                                <li>对象的 <strong>键</strong> 是要从JSON中提取数据的路径，支持用点(<code>.</code>)分隔的嵌套路径 (例如, <code>"user.address.city"</code>)。</li>
                                <li>对象的 <strong>值</strong> 是要将提取的数据存入的变量名。</li>
                                <li>如果指定的路径在JSON响应中不存在，脚本将报错并停止。</li>
                                <li>要获取完整的、未解析的原始响应文本（例如当响应不是JSON时），请使用特殊键 <code>"_raw"</code>。</li>
                            </ul>
                        </li>
                        <li><code>data</code> (可选): 请求体。如果提供此参数，请求将使用 <code>POST</code> 方法；否则使用 <code>GET</code>。如果 <code>data</code> 是一个对象，它将被自动序列化为JSON字符串，并且 <code>Content-Type</code> 头将默认为 <code>application/json</code>。</li>
                        <li><code>header</code> (可选): 一个包含请求头的对象。</li>
                        <li><code>timeout</code> (可选): 请求的超时时间（毫秒）。默认为 30000 (30秒)。</li>
                    </ul>
                    <p><strong>示例:</strong></p>
                    <pre>
{
  "command": "api_request",
  "url": "https://api.example.com/user/123",
  "header": { "Authorization": "Bearer ${token}" },
  "data": { "data": "${data}" },
  "timeout": 15000,
  "outputs": {
    "name": "username",
    "profile.age": "user_age",
    "roles[0]": "first_role"
  }
}
                    </pre>
                </li>
            </ul>
            
            <hr>

            <h3>高级选择器</h3>
            <p>对于所有需要 <code>selector</code> 的指令，除了标准的CSS选择器和XPath字符串，您还可以使用一个功能更强大的JSON对象来进行更稳定和精确的定位。</p>
            <pre>
{
    "command": "click",
    "selector": {
        "by": "data-testid",
        "value": "submit-button",
        "index": -1
    }
}
            </pre>
            <p><strong>对象参数:</strong></p>
            <ul>
                <li>
                    <strong><code>by</code> (必需):</strong> 您想通过哪个HTML属性来查找元素。这可以是任何有效属性，例如 <code>"data-testid"</code>, <code>"data-cy"</code>, <code>"name"</code>, <code>"title"</code> 等。支持使用 <code>${variableName}</code> 语法。
                </li>
                <li>
                    <strong><code>value</code> (必需):</strong> 您要查找的属性的值。支持使用 <code>${variableName}</code> 语法。
                </li>
                <li>
                    <strong><code>index</code> (可选):</strong> 当查找到多个匹配元素时，指定您想要操作哪一个。支持使用 <code>${variableName}</code> 语法（会被自动转换为数字）。
                    <ul>
                        <li><code>0</code>: 第一个元素 (默认值)。</li>
                        <li><code>1</code>: 第二个元素。</li>
                        <li><code>-1</code>: 倒数第一个元素。</li>
                        <li><code>-2</code>: 倒数第二个元素。</li>
                    </ul>
                </li>
            </ul>

            <hr>

            <h3>通用可选参数</h3>
            <p>以下参数适用于所有需要与页面元素交互的指令 (<code>click</code>, <code>type</code>, <code>scrape_text</code>, <code>scrape_html</code>, <code>goto</code>)。</p>
            <ul>
                <li>
                    <strong><code>continueOnError</code> (出错时继续)</strong>
                    <p>一个布尔值 (<code>true</code> 或 <code>false</code>)。如果设置为 <code>true</code>，当指令因超时（例如页面加载超时或元素未找到）而失败时，脚本将不会停止，而是会继续执行下一步。对于 <code>scrape_text</code> 和 <code>scrape_html</code>，如果元素未找到，目标变量将被设置为 <code>null</code>。默认为 <code>false</code>（即遇到错误就停止）。</p>
                </li>
                <li>
                    <strong><code>timeout</code> (超时)</strong>
                    <p>在放弃并报错之前，等待一个条件完成的最长时间（毫秒）。</p>
                    <ul>
                        <li>对于 <code>click</code>, <code>type</code>, <code>scrape_text</code>，这是等待元素出现的时间。默认为 0（不等待）。</li>
                        <li>对于 <code>goto</code> 和 <code>click</code>（在点击后），这是等待页面加载完成的时间。默认为 30000 （30秒）。</li>
                    </ul>
                </li>
                <li>
                    <strong><code>stability</code> (稳定)</strong>
                    <p>找到元素后，额外等待元素位置和大小保持不变的时间（毫秒），以确保动画等效果已结束。默认为 0。</p>
                </li>
            </ul>
        </div>
    </details>
    
    <br>
    <button id="saveButton">保存脚本</button>
    <div id="message"></div>
    <script src="options.js"></script>
</body>
</html>
