document.addEventListener('DOMContentLoaded', async () => {
    const scriptEditor = document.getElementById('scriptEditor');
    const saveButton = document.getElementById('saveButton');
    const messageDiv = document.getElementById('message');

    async function loadDefaultScript() {
        try {
            const url = chrome.runtime.getURL('defaults/default_script.json');
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`网络错误：${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            return [{ "command": "error", "description": `无法加载默认脚本。${error.message}` }];
        }
    }

    // 加载保存的脚本或者默认的脚本
    const storageData = await chrome.storage.sync.get(['automationScript']);
    if (storageData.automationScript) {
        scriptEditor.value = JSON.stringify(storageData.automationScript, null, 2);
    } else {
        const defaultScript = await loadDefaultScript();
        scriptEditor.value = JSON.stringify(defaultScript, null, 2);
    }

    // 保存脚本
    saveButton.addEventListener('click', () => {
        let script;
        try {
            script = JSON.parse(scriptEditor.value);
            if (!Array.isArray(script)) {
                throw new Error("脚本必须是一个JSON数组。");
            }
        } catch (error) {
            showMessage(`保存失败：无效的JSON格式。${error.message}`, 'error');
            return;
        }

        chrome.storage.sync.set({ automationScript: script }, () => {
            showMessage('脚本已成功保存！', 'success');
        });
    });

    function showMessage(text, type) {
        messageDiv.textContent = text;
        messageDiv.className = type; // 'success'或'error'
        setTimeout(() => {
            messageDiv.className = '';
            messageDiv.textContent = '';
        }, 3000); // 3秒后隐藏
    }
});
