body {
    font-family: sans-serif;
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.6;
}

h1 {
    text-align: center;
}

#scriptEditor {
    width: 100%;
    box-sizing: border-box; /* Ensures padding and border are included in the width */
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 10px;
    font-family: monospace;
}

details {
    margin-top: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
}

summary {
    cursor: pointer;
    font-weight: bold;
}

.instructions {
    margin-top: 15px;
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
}

.instructions h3 {
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.instructions h4 {
    margin-top: 20px;
    margin-bottom: 10px;
    color: #495057;
}

.concept {
    background-color: #e9ecef;
    border-left: 4px solid #007BFF;
    padding: 10px 15px;
    margin-bottom: 15px;
}

.instructions ul {
    list-style-type: none;
    padding-left: 0;
}

.instructions > ul > li {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 15px;
    padding: 15px;
}

.instructions li strong {
    font-family: 'Courier New', Courier, monospace;
    color: #c7254e;
    background-color: #f9f2f4;
    padding: 2px 4px;
    border-radius: 4px;
    font-size: 0.9em;
}

.instructions li p {
    margin: 10px 0;
}

.instructions li ul {
    margin-top: 10px;
    padding-left: 20px;
    border-left: 2px solid #eee;
}

.instructions li ul li {
    margin-bottom: 5px;
}

.instructions p.notice {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
    padding: 10px;
    color: #856404;
}

.instructions pre {
    background-color: #f4f4f4;
    padding: 10px;
    border-radius: 4px;
    white-space: pre-wrap; /* Ensures long lines wrap */
    word-break: break-all;
}

#saveButton {
    padding: 10px 20px;
    margin-top: 10px;
    border: none;
    border-radius: 4px;
    background-color: #007BFF;
    color: white;
    cursor: pointer;
    font-size: 16px;
}

#saveButton:hover {
    background-color: #0056b3;
}

#message {
    margin-top: 15px;
    padding: 10px;
    border-radius: 4px;
    display: none; /* Hidden by default */
}

#message.success {
    background-color: #d4edda;
    color: #155724;
    display: block;
}

#message.error {
    background-color: #f8d7da;
    color: #721c24;
    display: block;
}
