[{"command": "init_variable", "variableName": "工作票编号", "type": "string", "value": "待识别", "description": "初始化工作票编号"}, {"command": "init_variable", "variableName": "校核时间", "type": "string", "value": "待输出", "description": "初始化校核时间"}, {"command": "init_variable", "variableName": "校核结果（简要）", "type": "string", "value": "待生成", "description": "初始化校核结果（简要）"}, {"command": "init_variable", "variableName": "校核结果（详细）", "type": "string", "value": "待生成", "description": "初始化校核结果（详细）"}, {"command": "init_variable", "variableName": "不合格项数", "type": "number", "value": 0, "description": "初始化不合格项数"}, {"command": "init_variable", "variableName": "不规范项数", "type": "number", "value": 0, "description": "初始化不规范项数"}, {"command": "init_variable", "variableName": "需要人工确认项数", "type": "number", "value": 0, "description": "初始化需要人工确认项数"}, {"command": "init_variable", "variableName": "工作票校核序号", "type": "number", "value": 1, "description": "初始化工作票校核序号"}, {"command": "init_variable", "variableName": "工作票最大序号", "type": "number", "value": 28, "description": "初始化工作票最大序号"}, {"label": "loop_start", "description": "开始工作票校核"}, {"command": "switch_iframe", "continueOnError": "true", "description": "切换到工作票iframe"}, {"clickDelay": 500, "clicks": 2, "command": "click", "selector": "#view_grid_cont_bodyTable > tbody > tr:nth-child(${工作票校核序号})", "timeout": 5000, "description": "打开工作票"}, {"command": "wait", "milliseconds": 2000, "description": "等待工作票新页面打开"}, {"command": "switch_tab", "description": "开始工作票抓取", "title_pattern": "打印工作票*"}, {"command": "scrape_html", "selector": "#打印工作票（修改） > tbody", "outputVariable": "抓取到的HTML", "timeout": 5000, "description": "等待和抓取工作票"}, {"command": "api_request", "url": "http://************:2345/validate", "data": "${抓取到的HTML}", "header": {"Content-Type": "text/html; charset=utf-8"}, "timeout": 10000, "outputs": {"ticket_id": "工作票编号", "check_time": "校核时间", "stats.不合格": "不合格项数", "stats.不规范": "不规范项数", "stats.请人工确认": "需要人工确认项数", "summary_brief": "校核结果（简要）", "summary_detailed": "校核结果（详细）", "results": "校核结果（原始）"}, "description": "自动校核工作票"}, {"command": "wait_for_user", "description": "等待人工确认"}, {"command": "calculate", "expression": "工作票校核序号 + 1", "outputVariable": "工作票校核序号", "description": "更新工作票校核序号"}, {"command": "conditional_jump", "condition": "工作票校核序号 > 工作票最大序号", "targetLabel": "loop_end", "description": "判断是否校核完所有工作票"}, {"closePreviousTab": "true", "command": "switch_tab", "description": "未校核完，继续校核下一张工作票"}, {"command": "jump", "targetLabel": "loop_start", "description": "继续校核"}, {"label": "loop_end", "description": "所有工作票校核完成"}]