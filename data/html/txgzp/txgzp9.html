<html xmlns:iewc=""><head><meta http-equiv="X-UA-Compatible" content="IE=5">
<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE5">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">




<!--?import namespace="iewc" implementation="TabStrip.htc"-->
<!--?import namespace="iewc" implementation="MultiPage.htc"-->


<title>打印工作票（修改）</title>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<style>
a,b,body,div,td,u {
	font-family: verdana;
	font-size: 12px;
}

a {
	color: royalblue;
}

img {
	border: 0px;
}

col {
	background: transparent;
}
</style>
<script>
    	// 2009-06-09.Hubin WebLogic9.2.3下通过request.getContextPath()的地址前面有个两个反斜杠
    	//$appRoot = "/MWWebSite/";
    	$appRoot = "/MWWebSite/";
    	$mxRoot = $appRoot + "scripts/"; 
    </script>
<script type="text/javascript" src="scripts/_FixPrototype.js"></script>
<script type="text/javascript" src="scripts/framework.js"></script><script id="/MWWebSite/scripts//util/Dictionary.js" src="/MWWebSite/scripts//util/Dictionary.js"></script><script id="/MWWebSite/scripts//dom/HtmlDomUtil.js" src="/MWWebSite/scripts//dom/HtmlDomUtil.js"></script><script id="/MWWebSite/scripts//controls/Control.js" src="/MWWebSite/scripts//controls/Control.js"></script><script id="/MWWebSite/scripts//PortalClass.js" src="/MWWebSite/scripts//PortalClass.js"></script><script id="/MWWebSite/scripts//Config.js" src="/MWWebSite/scripts//Config.js"></script>
<script type="text/javascript" src="scripts/src/controls/chart.js"></script>
<script type="text/javascript" src="scripts/ReportBrowser.js"></script>
<script type="text/javascript" src="/MWWebSite/PROJECT-HOME/nwoms/scripts/My97DatePicker/WdatePicker.js"></script><link href="http://**********:7001/MWWebSite/PROJECT-HOME/nwoms/scripts/My97DatePicker/skin/WdatePicker.css" rel="stylesheet" type="text/css">
<script> 
        var chartArray=new Array();	
        var printchartArray=new Array();	
    	var currentUserID = "null";
		var currentUserName = "null";
		var reportID = "6B9568B7-7B6F-45E7-A65B-36CDDB13AA2A";//"6B9568B7-7B6F-45E7-A65B-36CDDB13AA2A";
		
		
		// 2009-06-08.Hubin 全局对象，可监听报表交互事件
		var reportBrowser = new ReportBrowser();
    	function setParamLabels()
    	{
    		var paramsWithValidValues = document.getElementsByTagName("select");
			for (var i = 0; i < paramsWithValidValues.length; i++)
            {
            	var param = paramsWithValidValues.item(i);
            	document.getElementById(param.name + "_LabelInput").value = param.options[param.selectedIndex].innerText;
            }
    	}
    	
    	function callBack(p_url, p_param)
	    {
	        var http = new ActiveXObject("Microsoft.XMLHTTP");
	        http.open("POST", p_url, false);
	        http.send(p_param);
	        if(http.status != '200')
	        {
	            throw new Error(http.responsetext);
	        }
	        return http.responsetext;
	    }
	    
	    function _adjustFlowItemPosition(p_itemName)
        {
	    	//wangting std.38822 2014-07-17 修正浮动图片框偏移问题 
            /*var flowItem = document.getElementById(p_itemName);
            if(!isEmpty(flowItem) && !isEmpty(flowItem.parentNode)) 
            { 
                flowItem.style.left = $left(flowItem.parentNode) + parseInt(flowItem.style.left);
                flowItem.style.top = $top(flowItem.parentNode) + parseInt(flowItem.style.top);
            } */
        }
        
        function resetToolBarPosition()
        {
        	if (document.all.ReportBrowserToolBar)
        	{
	        	var top=document.body.scrollTop;
				document.all.ReportBrowserToolBar.style.top = top; 
				// 2009-09-10.Hubin 工具栏在横向上也保持静止
				// 2009-10-18.Hubin 当工具栏的宽度大于浏览器宽度时，拖动时不保持静止
				if ($width(document.all.ReportBrowserToolBar) <= document.body.clientWidth)
				{
					var left = document.body.scrollLeft;
					document.all.ReportBrowserToolBar.style.left = left; 
				}
			}
        }
    </script>
<script type="text/javascript" src="scripts/src/controls/MW_FlowPanel.js"></script>
<script type="text/javascript" src="scripts/detailAddingDeleting.js"></script>
<script>
        $import("mw.controls.Calendar");
    </script><script id="/MWWebSite/scripts//controls/Calendar.js" src="/MWWebSite/scripts//controls/Calendar.js"></script><script id="/MWWebSite/scripts//dom/HtmlWriter.js" src="/MWWebSite/scripts//dom/HtmlWriter.js"></script><script id="/MWWebSite/scripts//dom/DomWriter.js" src="/MWWebSite/scripts//dom/DomWriter.js"></script>
<script>
        $import("mw.controls.DateTimePicker");
    </script><script id="/MWWebSite/scripts//controls/DateTimePicker.js" src="/MWWebSite/scripts//controls/DateTimePicker.js"></script>
<script>
    	$import("mw.controls.Tooltip");
    </script><script id="/MWWebSite/scripts//controls/Tooltip.js" src="/MWWebSite/scripts//controls/Tooltip.js"></script>
<script>
    	$import("mw.controls.ComboBox");
    </script><script id="/MWWebSite/scripts//controls/ComboBox.js" src="/MWWebSite/scripts//controls/ComboBox.js"></script><script id="/MWWebSite/scripts//controls/FlowContainer.js" src="/MWWebSite/scripts//controls/FlowContainer.js"></script><script id="/MWWebSite/scripts//net/WebRequest.js" src="/MWWebSite/scripts//net/WebRequest.js"></script><script id="/MWWebSite/scripts//net/WebResponse.js" src="/MWWebSite/scripts//net/WebResponse.js"></script>
</head>
<body style="margin: 0px 0px 0px 0px;" align="center">
	
<form id="ParamsInput" name="ParamsInput" method="post" onsubmit="setParamLabels()">        
<div id="ReportBrowserToolBar" name="ReportBrowserToolBar" style="position:absolute;width:100%;background-color:#e1e1e1;border-bottom:solid 1px gray;padding:4px 0px 3px 10px;">
			<nobr>
				<table>
					<tbody><tr>
						
			























<td>	
<input type="image" name="saveaspdf" title="另存为PDF文件" onclick="SaveAsPdfClick();event.returnValue=false;" onmouseover="this.style.backgroundColor='#bbe1f4';this.style.border='solid 1px gray';" onmouseout="this.style.backgroundColor='transparent';this.style.border='solid 1px #e1e1e1'" src="images/gen_pdf.png" align="absmiddle" style="font-family:宋体;
		font-size:9pt;
		border-width:0px;
		border:solid 1px #e1e1e1;
		background-color:#e1e1e1;
		cursor:hand;
		magin:0px 4px 0px 4px;
		padding:3px 3px 3px 3px;">
</td>



<td>
<input type="image" name="print" title="打印" onmouseover="this.style.backgroundColor='#bbe1f4';this.style.border='solid 1px gray';" onmouseout="this.style.backgroundColor='transparent';this.style.border='solid 1px #e1e1e1'" src="images/gen_print.png" align="absmiddle" onclick="PrintPreviewClick();event.returnValue=false;" style="font-family:宋体;
		font-size:9pt;
		border-width:0px;
		border:solid 1px #e1e1e1;
		background-color:#e1e1e1;
		cursor:hand;magin:0px 4px 0px 4px;
		padding:3px 3px 3px 3px;">
</td>



		


<script language="javascript"> 
   var fileRename = null;
   function openPostWindow(url,params,name)
   {
        var tempForm = document.createElement("form");
        tempForm.id="tempForm1";
        tempForm.method="post";
        tempForm.action = url;
        tempForm.target = name;
		for (var propName in params) {
		    if (typeof(params[propName]) != "function")
		    {
                var hideInput = document.createElement("input");
                hideInput.type = "hidden";
                hideInput.name = propName;
                hideInput.value = params[propName];
                tempForm.appendChild(hideInput);
			}
		}
        
        tempForm.attachEvent("onsubmit",function(){openwindow(name);});
        document.body.appendChild(tempForm);

        //tempForm.fireEvent("onsubmit");

        tempForm.submit();
        document.body.removeChild(tempForm);
   }
   function openwindow(name)
   {
        window.open('about:blank',"_self");
   }

	function displayHideChart(requestData)
	{
		for(var i=0;i<chartArray.length;i++)
		{
			var chart = chartArray[i];
			var chartdisplay =document.getElementById(chart.id);
			var chartprint =document.getElementById(chart.id+"_print");
			if(chartprint!=null)
			{
				chartprint.style.width = chartdisplay.offsetWidth*2;
				chartprint.style.height = chartdisplay.offsetHeight*2;
				alert(chart.id);
				alert(chartdisplay.offsetHeight);
				chartprint.style.display='';
	 			requestData[chart.id] = printchartArray[i].saveToPNG(); //chart.getJPEG();
//				chartprint.style.display='display:none';
			}
		}
		
	}

    var currentpos,timer,printtimer; 
    function initialize() 
    { 
      timer=setInterval("scrollwindow()",2); 
    } 
    function sc() 
    { 
	      clearInterval(timer); 
	      if(typeof(tsHoriz)!="undefined"&&tsHoriz!=null&&tabselectindex<tsHoriz.numTabs)
	   	  {
				tabselectindex++;
				tsHoriz.selectedIndex=tabselectindex;
		    	timer=setInterval("scrollwindow()",2);     	
	   	  }
	      else{
                 printtimer=setInterval("displaychart()",1000); 
	    	  
//				var requestData = {};
	  			
//	 		     var idwithguid = "";
//	 	     for(var i=0;i<chartArray.length;i++)
//	 	     {
//	 	        var chartid = chartArray[i].id;
//	 			requestData[chartid] = chartArray[i].saveToPNG(); //chart.getJPEG();
//	 	     }
//	 	     requestData["______reportid"] = "6B9568B7-7B6F-45E7-A65B-36CDDB13AA2A";
//	 	     if(saveType=="word")
//		 		openPostWindow("SaveAsWord.jsp",requestData,"_self");
//	 	     else	if(saveType=="pdf")
//		 		openPostWindow("SaveAsPdf.jsp",requestData,"_self");
//	 	     else	if(saveType=="excel")
//		 		openPostWindow("SaveAsExcel.jsp",requestData,"_self");

	   	  }
    } 
    function scrollwindow() 
    { 
      currentpos=document.body.scrollTop;  
      currentpos+=30;
      window.scroll(0,currentpos); 
      if(currentpos != document.body.scrollTop)  //判断是否滚动到网页最下端,是则停止循环
      sc(); 
    }     
    var printChartIndex = -1;
    var fileName = "";//2013-10-24 wangting std.35784 设置导出报表的文件名
    function displaychart()
    {
    	if(chartArray.length > 0 && printChartIndex<chartArray.length-1)
   		{
    		if(printChartIndex>=0)
   			{
    			var chart = chartArray[printChartIndex];
    			var chartprint =document.getElementById(chart.id+"_print");
				chartprint.style.display='none';
   			}
   			printChartIndex++;
			var chart = chartArray[printChartIndex];
			var chartdisplay =document.getElementById(chart.id);
			var chartprint =document.getElementById(chart.id+"_print");
			if(chartprint!=null)
			{
				chartprint.style.width = chartdisplay.offsetWidth*2;
				chartprint.style.height = chartdisplay.offsetHeight*2;
				chartprint.style.display='';
			}
   		}
    	else
   		{
  	      	clearInterval(printtimer); 
    		if(chartArray.length > 0 && printChartIndex==chartArray.length-1)
   			{
    			var chart = chartArray[printChartIndex];
    			var chartprint =document.getElementById(chart.id+"_print");
				chartprint.style.display='none';
   			}
    		//-------------------------------------------------------------------------------------------------
    		var args = {};
    		args.requestData = {};
    		args.params = {};
    		args.cancel = false;
    		args.saveType = saveType;
    		args.requestData["______reportid"] = "6B9568B7-7B6F-45E7-A65B-36CDDB13AA2A";
    		//2013-10-24 wangting std.35784 设置导出报表的文件名
    		if((fileName == null) || (fileName == ""))
    		{
    			args.requestData["______filename"] = encodeURI(encodeURI(title));//wangting std.37882 2014-03-14
    		}
    		else
    		{
				if ((fileName.indexOf("'") != -1)
						|| (fileName.indexOf("<") != -1)
						|| (fileName.indexOf("=") != -1)
						|| (fileName.indexOf(">") != -1)
						|| (fileName.indexOf("</script") != -1)
						|| (fileName.indexOf("<script") != -1)
						|| (fileName.indexOf("script>") != -1)
						|| (fileName.indexOf("script:") != -1)
						|| (fileName.indexOf("style=") != -1)) {
					alert("文件名包含非法字符，设置无效！");//wangting 2013-12-13 安全扫描问题
					args.requestData["______filename"] = encodeURI(encodeURI(title));//wangting std.37882 2014-03-14
				} else {
					args.requestData["______filename"] = encodeURI(encodeURI(fileName));
				}
			}
			//end

    		var p_OBJ_ID = document.getElementById("OBJ_ID");
    		if(p_OBJ_ID && p_OBJ_ID.tagName.toLowerCase()=="input" && p_OBJ_ID.type.toLowerCase()=="text")
   			{
    			args.params["OBJ_ID"] = p_OBJ_ID.value;
   			}
			
    		// modified by jhm. 2012-2-24
    		// 解决图表导出word文字模糊问题。
	  		for(var i=0;i<chartArray.length;i++)
			{
				var chart = chartArray[i];
				if(printchartArray[i]!=null&&chart!=null)
				{
//				    args.requestData[chartid] = chartArray[i].saveToPNG(); //chart.getJPEG();
//					args.requestData[chart.id] = printchartArray[i].saveToPNG(); //chart.getJPEG();
					//cx std.35512 两tab页报表导出包含图片时报错
//					if (args.requestData[chart.id] == null)
					//cx std.37183  图片导出excel时坐标轴字体变小
					args.requestData[chart.id] = chartArray[i].saveToPNG();
				}
			}
	  		
    		if(typeof Report_onBeforeDownload != "undefined")
    		{
    			Report_onBeforeDownload(this,args);
    			if(args.cancel) return;
    		}
    		
	 	    if(saveType=="word")
		 	openPostWindow("SaveAsWord.jsp",args.requestData,"_self");
	 	    else	if(saveType=="pdf")
		 	openPostWindow("SaveAsPdf.jsp",args.requestData,"_self");
	 	    else	if(saveType=="excel")
		 	openPostWindow("SaveAsExcel.jsp",args.requestData,"_self");
	 	 //-------------------------------------------------------------------------------------------------
	 	    
			//var requestData = {};  	      	
	  		//for(var i=0;i<chartArray.length;i++)
			//{
				//var chart = chartArray[i];
				//if(printchartArray[i]!=null&&chart!=null)
				//{
		 		//	requestData[chart.id] = printchartArray[i].saveToPNG(); //chart.getJPEG();
				//}
		 	    //requestData["______reportid"] = "6B9568B7-7B6F-45E7-A65B-36CDDB13AA2A";
		 	    //if(saveType=="word")
			 	//openPostWindow("SaveAsWord.jsp",requestData,"_self");
		 	    //else	if(saveType=="pdf")
			 	//openPostWindow("SaveAsPdf.jsp",requestData,"_self");
		 	    //else	if(saveType=="excel")
			 	//openPostWindow("SaveAsExcel.jsp",requestData,"_self");
			//}
   		}
    }
    
   
    var tabselectindex = 0;
    var saveType="";
    function SaveAsWordClick()
    {
    	//2013-10-24 wangting std.35784 设置导出报表的文件名
    	if((typeof(tsHoriz) == "undefined") && (fileRename == true))
    	{
    		fileName = prompt("请输入文件名","");
		}
    	timer=setInterval("scrollwindow()",10);
		saveType = "word";
		tabselectindex = 0;		
   	    //timer=setInterval("scrollwindow()",10);   //2013-10-24 wangting std.35784 设置导出报表的文件名
   	
    } 
    function SaveAsPdfClick()
    {
    	//2013-10-24 wangting std.35784 设置导出报表的文件名
    	if((typeof(tsHoriz) == "undefined") && (fileRename == true))
    	{
    		fileName = prompt("请输入文件名","");
		}
    	timer=setInterval("scrollwindow()",10);
		saveType = "pdf";
		tabselectindex = 0;		
    	//timer=setInterval("scrollwindow()",10);     	
    } 
    
    function SaveAsExcelClick()
    {
    	//2013-10-24 wangting std.35784 设置导出报表的文件名
    	if((typeof(tsHoriz) == "undefined") && (fileRename == true))
    	{
    		fileName = prompt("请输入文件名","");
		}
		timer=setInterval("scrollwindow()",10);
		saveType = "excel";
		tabselectindex = 0;		
		//timer=setInterval("scrollwindow()",10);     	
    } 
    
    function PrintPreviewClick()
    {
        window.open("PrintPreview.jsp?reportid=6B9568B7-7B6F-45E7-A65B-36CDDB13AA2A&PrintMode=ActiveX","_blank");
    }
    

   
</script>
						
					</tr>
				</tbody></table>
			</nobr>
		</div>
	</form>
	



	
	<div id="ToolBarPlace" style="width: 100%; height: 48px;"></div>
	<script>
	// 2009-07-09.Hubin 动态设置报表体距离窗口顶部的距离
	if (ReportBrowserToolBar)
	{
		ToolBarPlace.style.height = ReportBrowserToolBar.offsetHeight + 2;
	}
</script>

	
	























<style type="text/css">
td.style1{border-top:0.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:Right;
vertical-align:Center;
font-family:宋体;
font-size:14.0pt;
font-weight : Bold;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2{border-top:0.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:Right;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3{border-top:0.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:Right;
vertical-align:General;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
overflow:hidden
}
td.style2style3style4{border-top:0.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:Left;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3style4style5{border-top:1.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:General;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
}
td.style2style3style4style5style6{border-top:1.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:General;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
}
td.style2style3style4style5style6style7{border-top:0.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:General;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
}
td.style2style3style4style5style6style7style8{border-top:0.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:General;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
}
td.style2style3style4style5style6style7style8style9{border-top:0.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:General;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
}
td.style2style3style4style5style6style7style8style9style10{border-top:0.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:General;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
}
td.style2style3style4style5style6style7style8style9style10style11{border-top:1.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:General;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
}
td.style2style3style4style5style6style7style8style9style10style11style12{border-top:0.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:General;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13{border-top:1.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:5.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14{border-top:0.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:Left;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:5.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15{border-top:0.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:5.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16{border-top:0.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17{border-top:1.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18{border-top:0.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:Left;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19{border-top:0.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20{border-top:1.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:General;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21{border-top:0.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:General;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22{border-top:0.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:General;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23{border-top:1.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:3.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24{border-top:1.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:Right;
vertical-align:Bottom;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25{border-top:1.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:Center;
vertical-align:Bottom;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26{border-top:0.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:Right;
vertical-align:Top;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
padding-top:2
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27{border-top:0.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:Center;
vertical-align:Top;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
padding-top:2
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28{border-top:0.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:Right;
vertical-align:Bottom;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29{border-top:1.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30{border-top:0.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:10.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31{border-top:0.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:Right;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32{border-top:0.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:Center;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33{border-top:0.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34{border-top:0.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:General;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:10.0px;
padding-right:2.0px;
padding-top:2
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35{border-top:1.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36{border-top:1.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
overflow:hidden
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37{border-top:1.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:Left;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38{border-top:1.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
overflow:hidden
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39{border-top:1.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40{border-top:1.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:10.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41{border-top:1.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:Left;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:10.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42{border-top:1.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:Center;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43{border-top:1.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:Center;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44{border-top:1.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:Center;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45{border-top:1.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:Center;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46{border-top:1.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:General;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
padding-top:2
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47{border-top:1.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:General;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
padding-top:2
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48{border-top:0.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:Left;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49{border-top:0.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:General;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
padding-top:2
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50{border-top:0.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:General;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
padding-top:2
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51{border-top:1.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:General;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
padding-top:2
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52{border-top:0.0pt Solid #000000;
border-bottom:1.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52style53{border-top:0.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52style53style54{border-top:1.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52style53style54style55{border-top:0.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:10.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52style53style54style55style56{border-top:0.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:0.0pt Solid #000000;
border-right:1.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52style53style54style55style56style57{border-top:0.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
td.style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52style53style54style55style56style57style58{border-top:1.0pt Solid #000000;
border-bottom:0.0pt Solid #000000;
border-left:1.0pt Solid #000000;
border-right:0.0pt Solid #000000;
color:#000000;
background-color:transparent;
text-align:General;
vertical-align:Center;
font-family:宋体;
font-size:9.0pt;
word-break:break-all;
padding-left:2.0px;
padding-right:2.0px;
}
.pagebreak{ page-break-after: always }
</style>
<div style="position:relative"><table id="打印工作票（修改）" align="center" width="722.0" cellpadding="0" cellspacing="0" style="border-collapse:collapse;table-layout:fixed;border-collapse:collapse;">
<thead>
</thead><colgroup><col width="40.0pt">
<col width="40.0pt">
<col width="137.0pt">
<col width="70.0pt">
<col width="60.0pt">
<col width="35.0pt">
<col width="60.0pt">
<col width="60.0pt">
<col width="60.0pt">
<col width="60.0pt">
<col width="60.0pt">
<col width="40.0pt">
</colgroup>
<tbody>
<tr height="28.0pt">
<td></td>
<td></td>
<td class="style1" colspan="6">调度机构主站端工作票&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr>
<tr height="10.0pt">
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr>
<tr height="54.0pt">
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td class="style2style3" colspan="2"><img src="RUN-INF/images/973A2CE3-6344-469C-816F-582650CE2F4B.Bmp"></td>
<td></td>
</tr>
<tr height="30.0pt">
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td class="style2">编号：</td>
<td class="style2style3style4" colspan="3">ZD-TX20221025001</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13" colspan="3">工作负责人(监护人)：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17" colspan="3">吴桂龙</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23" rowspan="3">计划工作时间</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24">自</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25" colspan="4">2022&nbsp;年&nbsp;10&nbsp;月&nbsp;26&nbsp;日&nbsp;10&nbsp;时&nbsp;00&nbsp;分</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14" colspan="2">单位和班组：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16" colspan="4">通信处，南网科研院，北京天元，中通服创立</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28" colspan="5"></td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15" colspan="3">工作负责人及工作班人员总数共</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18">4</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19" colspan="2">人</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26">至</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27" colspan="4">2022&nbsp;年&nbsp;10&nbsp;月&nbsp;26&nbsp;日&nbsp;20&nbsp;时&nbsp;00&nbsp;分</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29" colspan="12">工作班人员（不包括工作负责人）：</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30" colspan="12">陈勋（南网科研院）,钟万原（北京天元）,赵杨军（中通服创立）&nbsp;</td>
</tr>
<tr height="0.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31" colspan="10"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33"></td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29" colspan="12">工作任务：</td>
</tr>
<tr height="521.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34" colspan="12">通信运行管控系统部署在主调三区调度云上，根据2022年等保测评工作要求，需要做设备漏扫问题复测，测评人员采用自己的设备笔记本终端（IP：**********）接入229镜像机房D06机柜的交换机30口，对以下云资产IP进行扫描：<br&nbsp;><br>运行控制模块-WEB服务器1(************)<br&nbsp;><br>运行控制模块-WEB服务器2(************)<br&nbsp;><br>综合监视模块-WEB服务器1(************)<br&nbsp;><br>综合监视模块-WEB服务器2(************)<br&nbsp;><br>管控系统单点登陆服务-WEB服务器1(************)<br&nbsp;><br>管控系统单点登陆服务-WEB服务器2(************)<br&nbsp;><br>综合监视三区告警接收服务器1(************)<br&nbsp;><br>综合监视三区告警接收服务器2(************)<br&nbsp;><br>（综合监视、运行控制）接口服务器1(************)<br&nbsp;><br>（综合监视、运行控制）接口服务器2(************)<br&nbsp;><br>统计评价收资服务器1(************)<br&nbsp;><br>统计评价收资服务器2(************)<br&nbsp;><br>数据分析服务器(************)<br&nbsp;><br>掌上通APP应用服务器1(************)<br&nbsp;><br>掌上通APP应用服务器2(************)<br&nbsp;><br>掌上通APP接口服务器1(11.14.37.121)<br&nbsp;><br>掌上通APP接口服务器2(11.14.37.122)<br&nbsp;><br>应用服务监控服务器1(11.14.37.123)<br&nbsp;><br>应用服务监控服务器2(11.14.37.124)<br&nbsp;><br>（综合监视、运行控制）备份服务器(11.14.37.125)<br&nbsp;><br>（资源管理）Web服务器（BS）1(11.14.37.126)<br&nbsp;><br>（资源管理）Web服务器（BS）2(11.14.37.127)<br&nbsp;><br>（资源管理）应用服务器（CS）1(11.14.37.128)<br&nbsp;><br>（资源管理）应用服务器（CS）2(11.14.37.129)<br&nbsp;><br>资源管理接口服务器1（数据库）(11.14.37.130)<br&nbsp;><br>资源管理接口服务器2（测试服务器）(11.14.37.131)<br&nbsp;><br>资源管理数据备份服务器(11.14.37.132)<br&nbsp;><br>主调通信网运行智慧管控服务器01(11.14.59.164)<br&nbsp;><br>主调通信网运行智慧管控服务器02(11.14.59.165)<br&nbsp;><br>主调通信网运行智慧管控服务器03(11.14.59.166)<br&nbsp;><br>主调通信网运行智慧管控服务器04(11.14.59.167)<br&nbsp;><br>主调通信网运行智慧管控服务器05(11.14.59.168)<br&nbsp;><br></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></br&nbsp;></td>
</tr>
<tr height="0.0pt">
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35" colspan="2">工作地点：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36"><img src="RUN-INF/images/E4793ED8-6CED-423A-AF16-F82144073C20.Bmp"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37" colspan="5">2号楼自动化机房229镜像机房D06机柜&nbsp;</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38" colspan="4"><img src="RUN-INF/images/CB472143-73B7-420E-B684-9D35A400A17B.Bmp"></td>
</tr>
<tr height="50.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29" colspan="12">工作影响范围：</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34" colspan="12">不影响通信运行管控系统正常运行。&nbsp;</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39" rowspan="5" colspan="2">工作要求的安全措施：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39" colspan="10">硬件及工作环境,应设遮栏、应挂标示牌位置：</td>
</tr>
<tr height="50.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40" colspan="10">1、工作机柜悬挂“在此工作"标示牌，并在相邻机柜粘贴“运行设备”警示带。2、提前做好安全交底，现场做好工作监护，防止误碰误断等误操作。&nbsp;</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39" colspan="10">软件及数据：</td>
</tr>
<tr height="60.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41" colspan="10">无</td>
</tr>
<tr height="43.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35">附件：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39" colspan="9">无</td>
</tr>
<tr height="32.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" rowspan="2" colspan="2">签发</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42">工作票签发人签名：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43" colspan="3">杨志敏</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43">时间：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44" colspan="5">2022&nbsp;年&nbsp;10&nbsp;月&nbsp;25&nbsp;日&nbsp;17&nbsp;时&nbsp;19&nbsp;分</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42">工作票会签人签名：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43" colspan="3">许丹莉</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43">时间：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44" colspan="5">2022&nbsp;年&nbsp;10&nbsp;月&nbsp;25&nbsp;日&nbsp;18&nbsp;时&nbsp;06&nbsp;分</td>
</tr>
<tr height="34.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" rowspan="5" colspan="2">工作许可</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46" colspan="2">安全措施是否满足工作要求：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47" colspan="8">是</td>
</tr>
<tr height="34.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48" colspan="2">需补充或调整的安全措施：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49" colspan="8">无</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50" colspan="2">其他安全注意事项：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49" colspan="8">无</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46" colspan="2">工作许可人签名：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51" colspan="3">马光</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51" colspan="2">工作负责人签名：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47" colspan="3">吴桂龙</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52">时间：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33" colspan="9">2022&nbsp;年&nbsp;10&nbsp;月&nbsp;26&nbsp;日&nbsp;10&nbsp;时&nbsp;11&nbsp;分</td>
</tr>
<tr height="0.0pt">
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
<td></td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" rowspan="3" colspan="2">安全交代</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29" colspan="10">工作班组人员确认工作负责人所交代布置的工作任务、安全措施和作业安全注意事项。</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52style53" colspan="10"></td>
</tr>
<tr height="40.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52">工作班人员签名：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19" colspan="4">陈勋（南网科研院）,钟万原（北京天元）,赵杨军（中通服创立）</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19">时间：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33" colspan="4">2022&nbsp;年&nbsp;10&nbsp;月&nbsp;26&nbsp;日&nbsp;10&nbsp;时&nbsp;12&nbsp;分</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" rowspan="4" colspan="2">工作间断</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45">工作间断时间</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45">工作许可人</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="2">工作负责人</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="2">工作开工时间</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="2">工作许可人</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="2">工作负责人</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45">月&nbsp;&nbsp;日&nbsp;&nbsp;时&nbsp;&nbsp;分</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="2"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="2">月&nbsp;&nbsp;日&nbsp;&nbsp;时&nbsp;&nbsp;分</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="2"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="2"></td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45">月&nbsp;&nbsp;日&nbsp;&nbsp;时&nbsp;&nbsp;分</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="2"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="2">月&nbsp;&nbsp;日&nbsp;&nbsp;时&nbsp;&nbsp;分</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="2"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="2"></td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45">月&nbsp;&nbsp;日&nbsp;&nbsp;时&nbsp;&nbsp;分</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="2"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="2">月&nbsp;&nbsp;日&nbsp;&nbsp;时&nbsp;&nbsp;分</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="2"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="2"></td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" rowspan="11">工作变更</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" rowspan="4">工作任务</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52style53style54" colspan="10">不需变更安全措施下增加的工作内容：</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52style53style54style55" colspan="10"></td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16" colspan="2">工作许可人签名：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16" colspan="2"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16" colspan="2">工作负责人签名：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52style53style54style55style56" colspan="4"></td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16">时间：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52style53style54style55style56" colspan="9">年&nbsp;&nbsp;月&nbsp;&nbsp;日&nbsp;&nbsp;时&nbsp;&nbsp;分</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" rowspan="3">工作负责人</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17" colspan="2">工作票签发人签名：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17" colspan="2"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17" colspan="2">同意变更时间：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52style53style54" colspan="4">年&nbsp;&nbsp;月&nbsp;&nbsp;日&nbsp;&nbsp;时&nbsp;&nbsp;分</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16" colspan="2">原工作负责人签名：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16" colspan="2"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16" colspan="2">现工作负责人签名：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52style53style54style55style56" colspan="4"></td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16" colspan="2">工作许可人签名：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16" colspan="2"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16" colspan="2">时间：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52style53style54style55style56" colspan="4">年&nbsp;&nbsp;月&nbsp;&nbsp;日&nbsp;&nbsp;时&nbsp;&nbsp;分</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" rowspan="4">工作班成员</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="3">变更情况</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="2">工作许可人</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="2">工作负责人</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="3">变更时间</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="3"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="2"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="2"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="3">月&nbsp;&nbsp;日&nbsp;&nbsp;时&nbsp;&nbsp;分</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="3"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="2"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="2"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="3">月&nbsp;&nbsp;日&nbsp;&nbsp;时&nbsp;&nbsp;分</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="3"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="2"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="2"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" colspan="3">月&nbsp;&nbsp;日&nbsp;&nbsp;时&nbsp;&nbsp;分</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" rowspan="3" colspan="2">工作延期</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52style53style54style55style56style57">有效期延长到：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52style53style54style55style56" colspan="9">&nbsp;年&nbsp;&nbsp;月&nbsp;&nbsp;日&nbsp;&nbsp;时&nbsp;&nbsp;分</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52style53style54style55style56style57">工作许可人签名：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16" colspan="3"></td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16" colspan="2">工作负责人签名：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52style53style54style55style56" colspan="4"></td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52style53style54style55style56style57">时间：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52style53style54style55style56" colspan="9">&nbsp;年&nbsp;&nbsp;月&nbsp;&nbsp;日&nbsp;&nbsp;时&nbsp;&nbsp;分</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45" rowspan="3" colspan="2">工作票终结</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52style53style54style55style56style57style58">全部作业于</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17" colspan="4">10&nbsp;月&nbsp;26&nbsp;日&nbsp;19&nbsp;时&nbsp;03&nbsp;分</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52style53style54" colspan="5">结束，相关软硬件系统运行正常，无异常告警，</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52style53" colspan="10">相关措施已经解除，已恢复工作开始前状态。</td>
</tr>
<tr height="30.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34style35style36style37style38style39style40style41style42style43style44style45style46style47style48style49style50style51style52">工作负责人签名：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19" colspan="3">吴桂龙</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19" colspan="2">工作许可人签名：</td>
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33" colspan="4">马光</td>
</tr>
<tr height="20.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29" colspan="12">备注（工作班人员变更、安全交代补充签名、作废原因等）</td>
</tr>
<tr height="20.0pt">
<td class="style2style3style4style5style6style7style8style9style10style11style12style13style14style15style16style17style18style19style20style21style22style23style24style25style26style27style28style29style30style31style32style33style34" colspan="12"></td>
</tr>
</tbody>
</table>
</div>
<script>
 	var title = "ZD-TX20221025001";//wangting std.37882 2014-03-14
</script>
	

	

<div style="position: absolute; z-index: 19700; top: -1970px; left: -1970px;"><iframe src="http://**********:7001/MWWebSite/PROJECT-HOME/nwoms/scripts/My97DatePicker/My97DatePicker.htm" frameborder="0" border="0" scrolling="no" style="width: 186px; height: 197px;"></iframe></div><div style="position: absolute; font-family: arial; font-size: 12px; display: none; width: 200px; height: 200px; background-color: rgb(255, 255, 255); border: 1px solid rgb(0, 0, 0);"><table cellpadding="0" cellspacing="0" style="width: 100%; border: 0px none; border-collapse: collapse;"><tbody><tr height="20" bgcolor="#ECE9D8"><td id="btnPreviousYear" style="width: 28px; text-align: center; cursor: pointer; border: 1px solid white;">|&lt;</td><td style="width: 28px; text-align: center; cursor: pointer; border: 1px solid white;">&lt;</td><td align="center" style="border: 1px solid white;"><input maxlength="10" style="width: 95%;"></td><td style="width: 27px; text-align: center; cursor: pointer; border: 1px solid white;">&gt;</td><td id="btnNextYear" style="width: 27px; text-align: center; cursor: pointer; border: 1px solid white;">&gt;|</td></tr></tbody></table><table cellpadding="0" cellspacing="0" style="width: 100%; border-top: 0px none; border-right: 0px none; border-bottom: 0px none; border-image: initial; border-collapse: collapse; background-color: rgb(239, 239, 239); border-left: 1px solid black !important;"><thead><tr style="height: 18px; text-align: center; padding-top: 5px; background-color: rgb(221, 221, 221);"><td style="height: 18px; text-align: center; border-left: 1px solid white !important;">日</td><td>一</td><td>二</td><td>三</td><td>四</td><td>五</td><td>六</td></tr></thead><tbody><tr style="height: 18px; text-align: center; background-color: rgb(221, 221, 221);"><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td></tr><tr style="height: 18px; text-align: center; background-color: rgb(221, 221, 221);"><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td></tr><tr style="height: 18px; text-align: center; background-color: rgb(221, 221, 221);"><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td></tr><tr style="height: 18px; text-align: center; background-color: rgb(221, 221, 221);"><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td></tr><tr style="height: 18px; text-align: center; background-color: rgb(221, 221, 221);"><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td></tr><tr style="height: 18px; text-align: center; background-color: rgb(221, 221, 221);"><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td><td width="22" height="18" style="cursor: default; border-left: 1px solid white; border-top: 1px solid white; border-bottom: 1px solid white; background-color: rgb(239, 239, 239);"></td></tr></tbody></table><table cellpadding="0" cellspacing="0" style="width: 100.5%; border: 0px none; border-collapse: collapse;"><tbody><tr height="20" bgcolor="#ECE9D8"><td style="text-align: center; cursor: pointer; border: 1px solid white; width: 28px !important;">&lt;&lt;</td><td style="text-align: center; cursor: pointer; border: 1px solid white; width: 28px !important;">&lt;</td><td align="center" style="border: 1px solid white;"><input maxlength="8" style="width: 65px;"></td><td style="text-align: center; cursor: pointer; border: 1px solid white; width: 28px !important;">&gt;</td><td style="width: 28px; text-align: center; cursor: pointer; border: 1px solid white;">&gt;&gt;</td></tr></tbody></table><table cellpadding="0" cellspacing="0" style="width: 100.5%; border: 0px; border-collapse: collapse;"><tbody><tr height="25" bgcolor="#ECE9D8"><td align="center"><button id="btnOK" classname="button" style="width: 70px; height: 22px;">确  定</button><img style="width: 15px; height: 0px;"><button classname="button" style="width: 70px; height: 22px;">取  消</button></td></tr></tbody></table></div></body></html>