# 悬浮UI故障排除指南

## 问题诊断步骤

### 1. 基础检查

#### 1.1 扩展是否正确安装和启用
1. 打开Chrome扩展管理页面 (`chrome://extensions/`)
2. 确认"Approval RPA"扩展已启用
3. 确认扩展版本为最新版本
4. 如果有错误提示，记录错误信息

#### 1.2 权限检查
1. 在扩展详情页面，确认以下权限已授予：
   - 存储
   - 标签页
   - 脚本注入
   - 活动标签页
   - 所有网站访问权限

#### 1.3 文件完整性检查
确认以下文件存在且内容正确：
- `scripts/floating-ui.js`
- `scripts/floating-ui-simple.js`
- `styles/floating-ui.css`
- `manifest.json` (包含web_accessible_resources配置)

### 2. 使用调试页面

1. 在浏览器中打开 `debug.html`
2. 点击"重新检查"按钮
3. 查看各项状态指示器：
   - 🟢 绿色：正常
   - 🟡 黄色：警告
   - 🔴 红色：错误

### 3. 浏览器控制台检查

#### 3.1 打开开发者工具
1. 按F12或右键选择"检查"
2. 切换到"Console"标签页
3. 刷新页面并观察控制台输出

#### 3.2 查找关键日志信息
正常情况下应该看到：
```
加载简化版悬浮UI
初始化简化版悬浮UI
简化版悬浮UI初始化完成
悬浮UI样式加载完成
```

#### 3.3 常见错误信息及解决方案

**错误1**: `Failed to load resource: chrome-extension://...`
- **原因**: 文件路径错误或文件不存在
- **解决**: 检查manifest.json中的web_accessible_resources配置

**错误2**: `chrome.runtime is undefined`
- **原因**: 在非扩展环境中运行
- **解决**: 确保在安装了扩展的浏览器中测试

**错误3**: `SimpleFloatingUI is not defined`
- **原因**: 脚本加载失败
- **解决**: 检查网络连接和文件权限

### 4. 手动测试步骤

#### 4.1 测试popup功能
1. 点击扩展图标打开popup
2. 确认popup正常显示
3. 测试"显示悬浮UI"按钮是否有响应

#### 4.2 测试消息传递
在控制台中执行：
```javascript
chrome.runtime.sendMessage({command: 'getStatus'}, console.log);
```

#### 4.3 测试悬浮UI初始化
在控制台中执行：
```javascript
console.log('approvalFloatingUI:', window.approvalFloatingUI);
```

### 5. 常见问题及解决方案

#### 问题1: 点击"显示悬浮UI"按钮无响应

**可能原因**:
- Content script未正确注入
- 消息传递失败
- 悬浮UI初始化失败

**解决步骤**:
1. 刷新页面重新注入content script
2. 检查控制台是否有错误信息
3. 使用debug.html页面进行详细诊断

#### 问题2: 悬浮UI显示但样式错乱

**可能原因**:
- CSS文件加载失败
- 网页样式冲突
- 浏览器兼容性问题

**解决步骤**:
1. 检查floating-ui.css是否正确加载
2. 在控制台中检查元素样式
3. 尝试使用简化版本

#### 问题3: 悬浮UI功能按钮无效

**可能原因**:
- 事件监听器未正确附加
- chrome.runtime API调用失败
- Background script通信问题

**解决步骤**:
1. 检查控制台中的点击事件日志
2. 测试background script是否正常运行
3. 验证消息格式是否正确

### 6. 高级调试技巧

#### 6.1 启用详细日志
在content script中添加更多日志输出：
```javascript
console.log('Content script loaded');
console.log('FloatingUI status:', window.approvalFloatingUI);
```

#### 6.2 检查扩展背景页面
1. 打开 `chrome://extensions/`
2. 点击扩展的"详细信息"
3. 点击"检查视图"中的"背景页面"
4. 查看background script的控制台输出

#### 6.3 网络请求监控
1. 在开发者工具的"Network"标签页
2. 刷新页面
3. 查看是否有失败的资源请求

### 7. 重置和重新安装

如果以上步骤都无法解决问题：

#### 7.1 重置扩展
1. 在扩展管理页面禁用扩展
2. 重新启用扩展
3. 刷新测试页面

#### 7.2 重新安装扩展
1. 完全卸载扩展
2. 重新加载扩展文件
3. 重新测试功能

### 8. 报告问题

如果问题仍然存在，请收集以下信息：

1. **浏览器信息**:
   - Chrome版本
   - 操作系统版本

2. **错误信息**:
   - 控制台错误日志
   - 扩展错误信息
   - debug.html的检查结果

3. **重现步骤**:
   - 详细的操作步骤
   - 预期结果vs实际结果

4. **环境信息**:
   - 测试页面URL
   - 其他已安装的扩展
   - 浏览器设置（如是否禁用JavaScript等）

### 9. 临时解决方案

如果悬浮UI无法正常工作，可以：

1. **使用原有popup界面**: 原有的popup功能应该仍然正常工作
2. **使用简化版本**: 如果完整版本有问题，简化版本可能仍然可用
3. **手动刷新**: 在每次使用前刷新页面重新初始化

### 10. 预防措施

为避免类似问题：

1. **定期更新**: 保持扩展和浏览器为最新版本
2. **权限检查**: 定期检查扩展权限设置
3. **备份配置**: 保存重要的脚本配置
4. **测试环境**: 在不同页面和环境中测试功能
