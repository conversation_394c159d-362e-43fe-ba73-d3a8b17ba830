<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>悬浮UI调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .debug-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .debug-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .debug-button:hover {
            background: #0056b3;
        }
        .debug-button.success {
            background: #28a745;
        }
        .debug-button.danger {
            background: #dc3545;
        }
        .debug-log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-ok { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .status-unknown { background: #6c757d; }
    </style>
</head>
<body>
    <h1>悬浮UI调试页面</h1>
    
    <div class="debug-section">
        <h3>扩展状态检查</h3>
        <div id="extension-status">
            <p><span class="status-indicator status-unknown" id="extension-indicator"></span>扩展状态: <span id="extension-text">检查中...</span></p>
            <p><span class="status-indicator status-unknown" id="content-script-indicator"></span>Content Script: <span id="content-script-text">检查中...</span></p>
            <p><span class="status-indicator status-unknown" id="floating-ui-indicator"></span>悬浮UI: <span id="floating-ui-text">检查中...</span></p>
        </div>
        <button class="debug-button" onclick="checkExtensionStatus()">重新检查</button>
    </div>
    
    <div class="debug-section">
        <h3>悬浮UI控制</h3>
        <button class="debug-button success" onclick="showFloatingUI()">显示悬浮UI</button>
        <button class="debug-button danger" onclick="hideFloatingUI()">隐藏悬浮UI</button>
        <button class="debug-button" onclick="testFloatingUIFeatures()">测试功能</button>
    </div>
    
    <div class="debug-section">
        <h3>脚本控制</h3>
        <button class="debug-button success" onclick="startScript()">启动脚本</button>
        <button class="debug-button" onclick="pauseScript()">暂停脚本</button>
        <button class="debug-button danger" onclick="stopScript()">停止脚本</button>
        <button class="debug-button" onclick="getStatus()">获取状态</button>
    </div>
    
    <div class="debug-section">
        <h3>调试日志</h3>
        <button class="debug-button" onclick="clearLog()">清空日志</button>
        <button class="debug-button" onclick="exportLog()">导出日志</button>
        <div id="debug-log" class="debug-log">等待调试信息...\n</div>
    </div>
    
    <div class="debug-section">
        <h3>测试元素</h3>
        <p>这些元素可以用于测试自动化脚本:</p>
        <button class="debug-button" id="test-button-1">测试按钮1</button>
        <button class="debug-button" id="test-button-2">测试按钮2</button>
        <input type="text" placeholder="测试输入框" id="test-input" style="margin: 5px; padding: 8px;">
        <select id="test-select" style="margin: 5px; padding: 8px;">
            <option value="1">选项1</option>
            <option value="2">选项2</option>
            <option value="3">选项3</option>
        </select>
    </div>

    <script>
        let logContainer = document.getElementById('debug-log');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '[ERROR]' : type === 'warning' ? '[WARN]' : '[INFO]';
            const logMessage = `[${timestamp}] ${prefix} ${message}\n`;
            logContainer.textContent += logMessage;
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(logMessage);
        }
        
        function clearLog() {
            logContainer.textContent = '';
        }
        
        function exportLog() {
            const logContent = logContainer.textContent;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `floating-ui-debug-${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }
        
        function updateStatus(elementId, textId, status, text) {
            const indicator = document.getElementById(elementId);
            const textElement = document.getElementById(textId);
            
            indicator.className = `status-indicator status-${status}`;
            textElement.textContent = text;
        }
        
        function checkExtensionStatus() {
            log('开始检查扩展状态...');
            
            // 检查chrome.runtime是否可用
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                updateStatus('extension-indicator', 'extension-text', 'ok', '已连接');
                log('Chrome扩展API可用');
                
                // 检查是否能发送消息
                try {
                    chrome.runtime.sendMessage({ command: 'getStatus' }, (response) => {
                        if (chrome.runtime.lastError) {
                            updateStatus('content-script-indicator', 'content-script-text', 'error', '通信失败');
                            log('与background script通信失败: ' + chrome.runtime.lastError.message, 'error');
                        } else {
                            updateStatus('content-script-indicator', 'content-script-text', 'ok', '通信正常');
                            log('与background script通信成功');
                            log('当前状态: ' + JSON.stringify(response, null, 2));
                        }
                    });
                } catch (error) {
                    updateStatus('content-script-indicator', 'content-script-text', 'error', '发送消息失败');
                    log('发送消息到background script失败: ' + error.message, 'error');
                }
                
                // 检查悬浮UI状态
                if (window.approvalFloatingUI) {
                    updateStatus('floating-ui-indicator', 'floating-ui-text', 'ok', '已初始化');
                    log('悬浮UI实例存在');
                } else {
                    updateStatus('floating-ui-indicator', 'floating-ui-text', 'warning', '未初始化');
                    log('悬浮UI实例不存在', 'warning');
                }
            } else {
                updateStatus('extension-indicator', 'extension-text', 'error', '未连接');
                log('Chrome扩展API不可用', 'error');
            }
        }
        
        function showFloatingUI() {
            log('尝试显示悬浮UI...');
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({ command: 'show_floating_ui' }, (response) => {
                    if (chrome.runtime.lastError) {
                        log('显示悬浮UI失败: ' + chrome.runtime.lastError.message, 'error');
                    } else if (response && response.success) {
                        log('悬浮UI显示成功');
                    } else {
                        log('悬浮UI显示失败: ' + (response ? response.error : '未知错误'), 'error');
                    }
                });
            } else {
                log('Chrome扩展API不可用', 'error');
            }
        }
        
        function hideFloatingUI() {
            log('尝试隐藏悬浮UI...');
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({ command: 'hide_floating_ui' }, (response) => {
                    if (chrome.runtime.lastError) {
                        log('隐藏悬浮UI失败: ' + chrome.runtime.lastError.message, 'error');
                    } else if (response && response.success) {
                        log('悬浮UI隐藏成功');
                    } else {
                        log('悬浮UI隐藏失败: ' + (response ? response.error : '未知错误'), 'error');
                    }
                });
            } else {
                log('Chrome扩展API不可用', 'error');
            }
        }
        
        function testFloatingUIFeatures() {
            log('测试悬浮UI功能...');
            if (window.approvalFloatingUI) {
                log('直接调用悬浮UI方法');
                if (window.approvalFloatingUI.isVisible) {
                    window.approvalFloatingUI.hide();
                    log('悬浮UI已隐藏');
                } else {
                    window.approvalFloatingUI.show();
                    log('悬浮UI已显示');
                }
            } else {
                log('悬浮UI实例不存在', 'warning');
            }
        }
        
        function startScript() {
            log('发送启动脚本命令...');
            chrome.runtime.sendMessage({ command: 'start', isAutoApprove: true }, (response) => {
                if (chrome.runtime.lastError) {
                    log('启动脚本失败: ' + chrome.runtime.lastError.message, 'error');
                } else {
                    log('启动脚本命令已发送');
                }
            });
        }
        
        function pauseScript() {
            log('发送暂停脚本命令...');
            chrome.runtime.sendMessage({ command: 'pause' }, (response) => {
                if (chrome.runtime.lastError) {
                    log('暂停脚本失败: ' + chrome.runtime.lastError.message, 'error');
                } else {
                    log('暂停脚本命令已发送');
                }
            });
        }
        
        function stopScript() {
            log('发送停止脚本命令...');
            chrome.runtime.sendMessage({ command: 'stop' }, (response) => {
                if (chrome.runtime.lastError) {
                    log('停止脚本失败: ' + chrome.runtime.lastError.message, 'error');
                } else {
                    log('停止脚本命令已发送');
                }
            });
        }
        
        function getStatus() {
            log('获取当前状态...');
            chrome.runtime.sendMessage({ command: 'getStatus' }, (response) => {
                if (chrome.runtime.lastError) {
                    log('获取状态失败: ' + chrome.runtime.lastError.message, 'error');
                } else {
                    log('当前状态: ' + JSON.stringify(response, null, 2));
                }
            });
        }
        
        // 页面加载时自动检查状态
        window.addEventListener('load', () => {
            log('页面已加载，开始初始化检查...');
            setTimeout(checkExtensionStatus, 1000);
        });
        
        // 监听来自扩展的消息
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
                log('收到来自扩展的消息: ' + JSON.stringify(request));
                return true;
            });
        }
    </script>
</body>
</html>
