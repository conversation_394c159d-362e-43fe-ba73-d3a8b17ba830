﻿<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div id="main-container">
        <h3>审批自动化脚本插件</h3>
    <div id="variables-container" style="display: none;">
        <div class="variables-header">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-braces" viewBox="0 0 16 16">
                <path d="M2.114 8.063V7.9c1.005-.102 1.497-.615 1.497-1.6V4.503c0-1.094.39-1.538 1.354-1.538h.273V2h-.376C3.25 2 2.49 2.759 2.49 4.352v1.524c0 1.062-.39 1.558-1.354 1.558h-.273v1.12h.376c1.005 0 1.497.492 1.497 1.6v1.524c0 1.593.76 2.352 2.372 2.352h.376v-.99H4.846c-.964 0-1.354-.444-1.354-1.538V9.663c0-.984-.492-1.497-1.497-1.6zM13.886 7.9v.163c-1.005.103-1.497.616-1.497 1.6v1.524c0 1.094-.39 1.538-1.354 1.538h-.273v.99h.376c1.613 0 2.372-.759 2.372-2.352v-1.524c0-1.062.39-1.558 1.354-1.558h.273v-1.12h-.376c-1.005 0-1.497-.492-1.497-1.6V4.503c0-1.593-.76-2.352-2.372-2.352h-.376v.99h.273c.964 0 1.354.444 1.354 1.538v1.524c0 .984.492 1.497 1.497 1.6z"/>
            </svg>
            <span>当前变量</span>
        </div>
        <table id="variables-table">
            <thead>
                <tr>
                    <th>变量名</th>
                    <th>类型</th>
                    <th>值</th>
                </tr>
            </thead>
            <tbody id="variables-body">
                <!-- Variable rows will be inserted here -->
            </tbody>
        </table>
    </div>
    <div id="warnings-container" style="display: none;">
        <div class="warning-header">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-exclamation-triangle-fill" viewBox="0 0 16 16">
                <path d="M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/>
            </svg>
            <span>执行警告</span>
        </div>
        <ul id="warnings-list"></ul>
    </div>
    <div id="error-container" style="display: none;">
        <div class="error-header">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                <path d="M7.002 11a1 1 0 1 1 2 0 1 1 0 0 1-2 0zM7.1 4.995a.905.905 0 1 1 1.8 0l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 4.995z"/>
            </svg>
            <span>执行错误</span>
        </div>
        <div id="error-message"></div>
        <div id="error-details"></div>
    </div>
    <div id="completion-container" class="info-container" style="display: none;">
        <div class="completion-header">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle-fill" viewBox="0 0 16 16">
                <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
            </svg>
            <span>任务状态</span>
        </div>
        <div id="completion-content"></div>
    </div>
    <div id="task-list-container">
        <!-- Task list will be rendered here by popup.js -->
    </div>
    <div class="controls-main">
        <button id="startButton">启动</button>
        <button id="pauseButton" disabled>暂停</button>
        <button id="stopButton" disabled>终止</button>
    </div>
    <div class="controls-step">
        <button id="nextStepButton" disabled>下一步</button>
    </div>
    <hr>
    <div class="controls-config">
        <label>
            <input type="checkbox" id="autoApproveCheckbox" checked>
            自动执行
        </label>
        <label>
            <input type="checkbox" id="showWarningsCheckbox">
            显示警告
        </label>
        <button id="configButton">配置脚本</button>
    </div>
    <hr>
    <div class="controls-floating-ui">
        <button id="showFloatingUIButton">显示悬浮UI</button>
        <button id="hideFloatingUIButton">隐藏悬浮UI</button>
    </div>
    </div>

    <!-- 详细内容面板 -->
    <div id="detail-panel" class="detail-panel-container">
        <div class="detail-panel-header">
            <span>详细内容</span>
            <button id="close-detail-panel">&times;</button>
        </div>
        <div id="detail-panel-content" class="detail-panel-content"></div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
