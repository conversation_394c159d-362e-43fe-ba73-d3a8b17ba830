<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>审批自动化扩展测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-input {
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin: 5px;
            width: 200px;
        }
        .instructions {
            background: #e7f3ff;
            padding: 15px;
            border-left: 4px solid #007bff;
            margin: 20px 0;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>审批自动化扩展测试页面</h1>
    
    <div class="instructions">
        <h3>使用说明：</h3>
        <ol>
            <li>确保已安装并启用了审批自动化扩展</li>
            <li>点击浏览器工具栏中的扩展图标打开popup</li>
            <li>在popup中点击"显示悬浮UI"按钮</li>
            <li>悬浮UI应该会出现在页面右上角</li>
            <li>可以拖拽悬浮UI到任意位置</li>
            <li>可以最小化/展开悬浮UI</li>
            <li>在popup中点击"启动"按钮测试自动化脚本</li>
        </ol>
    </div>
    
    <div class="warning">
        <strong>注意：</strong>悬浮UI会显示当前的执行状态、变量值、任务列表等信息。如果没有运行脚本，某些区域可能为空。
    </div>
    
    <div class="test-section">
        <h3>测试元素区域</h3>
        <p>这些元素可以用于测试自动化脚本的交互功能：</p>
        
        <div>
            <label>测试输入框：</label>
            <input type="text" class="test-input" id="testInput" placeholder="输入一些文本">
        </div>
        
        <div>
            <label>测试按钮：</label>
            <button class="test-button" id="testButton1" onclick="alert('按钮1被点击了！')">测试按钮1</button>
            <button class="test-button" id="testButton2" onclick="alert('按钮2被点击了！')">测试按钮2</button>
        </div>
        
        <div>
            <label>测试选择框：</label>
            <select class="test-input" id="testSelect">
                <option value="option1">选项1</option>
                <option value="option2">选项2</option>
                <option value="option3">选项3</option>
            </select>
        </div>
        
        <div>
            <label>测试复选框：</label>
            <input type="checkbox" id="testCheckbox1"> 复选框1
            <input type="checkbox" id="testCheckbox2"> 复选框2
        </div>
    </div>
    
    <div class="test-section">
        <h3>测试表格</h3>
        <table border="1" style="border-collapse: collapse; width: 100%;">
            <thead>
                <tr>
                    <th>工作票编号</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="view_grid_cont_bodyTable">
                <tr>
                    <td>WO-2024-001</td>
                    <td>待审批</td>
                    <td><button class="test-button">查看详情</button></td>
                </tr>
                <tr>
                    <td>WO-2024-002</td>
                    <td>已审批</td>
                    <td><button class="test-button">查看详情</button></td>
                </tr>
                <tr>
                    <td>WO-2024-003</td>
                    <td>待审批</td>
                    <td><button class="test-button">查看详情</button></td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="test-section">
        <h3>iframe测试区域</h3>
        <iframe id="testIframe" src="data:text/html,<html><body><h2>这是iframe内容</h2><button onclick='alert(\"iframe按钮被点击\")'>iframe按钮</button></body></html>" 
                width="100%" height="150" style="border: 1px solid #ccc;"></iframe>
    </div>
    
    <div class="test-section">
        <h3>测试信息</h3>
        <div id="testInfo">
            <p><strong>页面标题：</strong><span id="pageTitle"></span></p>
            <p><strong>当前时间：</strong><span id="currentTime"></span></p>
            <p><strong>用户代理：</strong><span id="userAgent"></span></p>
        </div>
    </div>
    
    <script>
        // 填充测试信息
        document.getElementById('pageTitle').textContent = document.title;
        document.getElementById('currentTime').textContent = new Date().toLocaleString();
        document.getElementById('userAgent').textContent = navigator.userAgent;
        
        // 每秒更新时间
        setInterval(() => {
            document.getElementById('currentTime').textContent = new Date().toLocaleString();
        }, 1000);
        
        // 添加一些测试用的动态内容
        let clickCount = 0;
        document.getElementById('testButton1').addEventListener('click', () => {
            clickCount++;
            document.getElementById('testButton1').textContent = `测试按钮1 (点击${clickCount}次)`;
        });
    </script>
</body>
</html>
</html>
