<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>悬浮UI消息传递测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button.success {
            background: #28a745;
        }
        .test-button.danger {
            background: #dc3545;
        }
        .log-container {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status-box {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .instructions {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-left: 4px solid #bee5eb;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>悬浮UI消息传递测试页面</h1>
    
    <div class="instructions">
        <h3>测试说明：</h3>
        <ol>
            <li>确保扩展已安装并启用</li>
            <li>打开扩展popup，点击"显示悬浮UI"</li>
            <li>使用下面的按钮测试消息传递功能</li>
            <li>观察日志输出和悬浮UI的响应</li>
        </ol>
    </div>
    
    <div class="test-section">
        <h3>消息传递测试</h3>
        <button class="test-button" onclick="testFloatingUIMessage()">测试悬浮UI消息</button>
        <button class="test-button" onclick="testBackgroundMessage()">测试Background消息</button>
        <button class="test-button success" onclick="simulateStatusUpdate()">模拟状态更新</button>
        <button class="test-button danger" onclick="clearTestLog()">清空日志</button>
    </div>
    
    <div class="test-section">
        <h3>悬浮UI控制测试</h3>
        <button class="test-button success" onclick="sendStartCommand()">发送启动命令</button>
        <button class="test-button" onclick="sendPauseCommand()">发送暂停命令</button>
        <button class="test-button danger" onclick="sendStopCommand()">发送停止命令</button>
        <button class="test-button" onclick="sendGetStatusCommand()">获取状态</button>
    </div>
    
    <div class="test-section">
        <h3>当前状态</h3>
        <div class="status-box">
            <p><strong>悬浮UI实例:</strong> <span id="floating-ui-status">检查中...</span></p>
            <p><strong>消息桥接:</strong> <span id="message-bridge-status">检查中...</span></p>
            <p><strong>最后消息:</strong> <span id="last-message">无</span></p>
            <p><strong>最后响应:</strong> <span id="last-response">无</span></p>
        </div>
        <button class="test-button" onclick="checkStatus()">刷新状态</button>
    </div>
    
    <div class="test-section">
        <h3>消息日志</h3>
        <div id="message-log" class="log-container">等待消息...\n</div>
    </div>

    <script>
        let messageLog = document.getElementById('message-log');
        let messageCount = 0;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '[ERROR]' : type === 'warning' ? '[WARN]' : '[INFO]';
            const logMessage = `[${timestamp}] ${prefix} ${message}\n`;
            messageLog.textContent += logMessage;
            messageLog.scrollTop = messageLog.scrollHeight;
            console.log(logMessage);
        }
        
        function clearTestLog() {
            messageLog.textContent = '';
            messageCount = 0;
        }
        
        function checkStatus() {
            log('检查当前状态...');
            
            // 检查悬浮UI实例
            if (window.approvalFloatingUI) {
                document.getElementById('floating-ui-status').textContent = '已初始化 (' + 
                    (window.approvalFloatingUI.isVisible ? '可见' : '隐藏') + ')';
                log('悬浮UI实例存在，可见状态: ' + window.approvalFloatingUI.isVisible);
            } else {
                document.getElementById('floating-ui-status').textContent = '未初始化';
                log('悬浮UI实例不存在', 'warning');
            }
            
            // 检查消息桥接
            const hasEventListeners = document.hasEventListener ? 'true' : 'unknown';
            document.getElementById('message-bridge-status').textContent = '已设置';
            log('消息桥接状态检查完成');
        }
        
        function testFloatingUIMessage() {
            log('测试悬浮UI消息发送...');
            messageCount++;
            
            const testMessage = {
                command: 'test',
                data: 'test message ' + messageCount,
                timestamp: Date.now()
            };
            
            document.getElementById('last-message').textContent = JSON.stringify(testMessage);
            
            // 发送测试消息
            const event = new CustomEvent('floatingUIMessage', {
                detail: testMessage
            });
            document.dispatchEvent(event);
            
            log('已发送测试消息: ' + JSON.stringify(testMessage));
        }
        
        function testBackgroundMessage() {
            log('测试Background消息接收...');
            
            const testResponse = {
                type: 'statusUpdate',
                state: 'testing',
                currentStep: messageCount,
                timestamp: Date.now()
            };
            
            document.getElementById('last-response').textContent = JSON.stringify(testResponse);
            
            // 模拟来自background的消息
            const event = new CustomEvent('backgroundMessage', {
                detail: testResponse
            });
            document.dispatchEvent(event);
            
            log('已发送模拟Background消息: ' + JSON.stringify(testResponse));
        }
        
        function simulateStatusUpdate() {
            log('模拟状态更新...');
            
            const statusUpdate = {
                type: 'statusUpdate',
                state: 'running',
                currentStep: Math.floor(Math.random() * 10),
                variables: {
                    '测试变量1': { type: 'string', value: '测试值' + Date.now() },
                    '测试变量2': { type: 'number', value: Math.floor(Math.random() * 100) }
                },
                warnings: [],
                errorInfo: null,
                completionMessage: null
            };
            
            const event = new CustomEvent('backgroundMessage', {
                detail: statusUpdate
            });
            document.dispatchEvent(event);
            
            log('已发送状态更新: ' + JSON.stringify(statusUpdate, null, 2));
        }
        
        function sendStartCommand() {
            log('发送启动命令...');
            const event = new CustomEvent('floatingUIMessage', {
                detail: { command: 'start', isAutoApprove: true }
            });
            document.dispatchEvent(event);
        }
        
        function sendPauseCommand() {
            log('发送暂停命令...');
            const event = new CustomEvent('floatingUIMessage', {
                detail: { command: 'pause' }
            });
            document.dispatchEvent(event);
        }
        
        function sendStopCommand() {
            log('发送停止命令...');
            const event = new CustomEvent('floatingUIMessage', {
                detail: { command: 'stop' }
            });
            document.dispatchEvent(event);
        }
        
        function sendGetStatusCommand() {
            log('发送获取状态命令...');
            const event = new CustomEvent('floatingUIMessage', {
                detail: { command: 'getStatus' }
            });
            document.dispatchEvent(event);
        }
        
        // 监听消息事件
        document.addEventListener('floatingUIMessage', (event) => {
            log('监听到floatingUIMessage: ' + JSON.stringify(event.detail));
        });
        
        document.addEventListener('backgroundMessage', (event) => {
            log('监听到backgroundMessage: ' + JSON.stringify(event.detail));
        });
        
        // 页面加载时检查状态
        window.addEventListener('load', () => {
            log('页面已加载');
            setTimeout(checkStatus, 1000);
        });
        
        // 定期检查状态
        setInterval(checkStatus, 5000);
    </script>
</body>
</html>
