// 确保监听器只被附加一次
if (!window.hasAutomationListener) {
    window.hasAutomationListener = true;

    // 追踪当前的文档上下文，默认为顶层文档
    let currentDocument = document;

    // 注入悬浮UI的CSS样式
    function injectFloatingUIStyles() {
        if (document.getElementById('approval-floating-ui-styles')) {
            console.log('悬浮UI样式已存在');
            return; // 已经注入过了
        }

        console.log('注入悬浮UI样式');
        const link = document.createElement('link');
        link.id = 'approval-floating-ui-styles';
        link.rel = 'stylesheet';
        link.href = chrome.runtime.getURL('styles/floating-ui.css');
        link.onload = () => console.log('悬浮UI样式加载完成');
        link.onerror = (error) => console.error('悬浮UI样式加载失败:', error);
        document.head.appendChild(link);
    }

    // 直接在content script中加载floating-ui代码
    async function loadFloatingUIScript() {
        if (window.approvalFloatingUI) {
            console.log('悬浮UI已存在');
            return; // 已经注入过了
        }

        console.log('开始加载悬浮UI脚本');

        try {
            // 先尝试加载简化版本进行测试
            const response = await fetch(chrome.runtime.getURL('scripts/floating-ui-simple.js'));
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const scriptContent = await response.text();
            console.log('脚本内容已获取，长度:', scriptContent.length);

            // 在当前上下文中执行脚本
            eval(scriptContent);
            console.log('脚本已执行');

            // 初始化悬浮UI
            if (window.SimpleFloatingUI) {
                window.approvalFloatingUI = new window.SimpleFloatingUI();
                console.log('简化版悬浮UI已初始化');
            } else {
                console.error('SimpleFloatingUI类未找到');
            }
        } catch (error) {
            console.error('加载悬浮UI脚本失败:', error);

            // 如果简化版本失败，尝试原版本
            try {
                console.log('尝试加载原版悬浮UI');
                const response = await fetch(chrome.runtime.getURL('scripts/floating-ui.js'));
                const scriptContent = await response.text();
                eval(scriptContent);

                if (window.FloatingUI) {
                    window.approvalFloatingUI = new window.FloatingUI();
                    console.log('原版悬浮UI已初始化');
                } else {
                    console.error('FloatingUI类未找到');
                }
            } catch (fallbackError) {
                console.error('加载原版悬浮UI也失败:', fallbackError);
            }
        }
    }

    // 初始化悬浮UI
    function initFloatingUI() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                injectFloatingUIStyles();
                loadFloatingUIScript();
            });
        } else {
            injectFloatingUIStyles();
            loadFloatingUIScript();
        }
    }

    // 启动悬浮UI初始化
    initFloatingUI();

    function getElementBySelector(selector) {
        // 字符串选择器（CSS/XPath）的兼容
        if (typeof selector === 'string') {
            if (selector.startsWith('/') || selector.startsWith('(')) {
                // 使用 currentDocument 作为 XPath 的上下文节点
                const result = currentDocument.evaluate(selector, currentDocument, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
                return result.singleNodeValue;
            } else {
                // 使用 currentDocument 进行查询
                return currentDocument.querySelector(selector);
            }
        }

        // 处理对象选择器
        if (typeof selector === 'object' && selector !== null) {
            const { by, value, index = 0 } = selector;
            
            if (!by || !value) {
                return null; // 'by' 和 'value' 是必需的
            }

            // 动态构建属性选择器，支持任何属性
            const attributeSelector = `[${by}="${value}"]`;
            // 使用 currentDocument 进行查询
            const elements = Array.from(currentDocument.querySelectorAll(attributeSelector));

            if (elements.length === 0) {
                return null;
            }

            let targetIndex = index;
            // 处理负数索引（倒序）
            if (targetIndex < 0) {
                targetIndex = elements.length + targetIndex;
            }

            // 检查索引是否在有效范围内
            if (targetIndex >= 0 && targetIndex < elements.length) {
                return elements[targetIndex];
            } else {
                return null; // 索引越界
            }
        }

        return null; // 无效的选择器格式
    }

    async function isElementStable(element, stabilityDelay) {
        const initialRect = element.getBoundingClientRect();
        await new Promise(resolve => setTimeout(resolve, stabilityDelay));
        const finalRect = element.getBoundingClientRect();

        return (
            initialRect.top === finalRect.top &&
            initialRect.right === finalRect.right &&
            initialRect.bottom === finalRect.bottom &&
            initialRect.left === finalRect.left &&
            initialRect.width === finalRect.width &&
            initialRect.height === finalRect.height
        );
    }

    async function waitForElement(selector, timeout, stabilityCheck) {
        const startTime = Date.now();
        let element = null;
    
        do {
            element = getElementBySelector(selector);
            if (element) {
                if (stabilityCheck > 0) {
                    if (await isElementStable(element, stabilityCheck)) {
                        return element; // 找到且稳定，返回
                    }
                } else {
                    return element; // 找到且不需要稳定检查，返回
                }
            }
            if (Date.now() - startTime < timeout) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        } while (Date.now() - startTime < timeout);
    
        const selectorString = typeof selector === 'string' ? selector : JSON.stringify(selector);
        throw new Error(`选择器为 ${selectorString} 的元素超时（${timeout}ms）未找到或在${stabilityCheck}ms内无法保持稳定。`);
    }


    const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
        window.HTMLInputElement.prototype,
        'value'
    ).set;

    function reactJsEvent(element, value) {
        if (!element._valueTracker) return;

        const previousValue = element.value;
        nativeInputValueSetter.call(element, value);
        element._valueTracker.setValue(previousValue);
    }

    async function typeInElement(element, value) {
        highlightElement(element, true);
        await new Promise(resolve => setTimeout(resolve, 500)); // 保持高亮0.5s
        element.focus();
        element.click();

        const isContentEditable = element.isContentEditable;
        const elementKey = isContentEditable ? 'textContent' : 'value';

        if (elementKey === 'value') {
            reactJsEvent(element, '');
            element.value = '';
        } else {
            element.textContent = '';
        }
        
        for (const char of value) {
            if (elementKey === 'value') reactJsEvent(element, element.value);
            
            if (isContentEditable && document.execCommand) {
                document.execCommand('insertText', false, char);
            } else {
                element[elementKey] += char;
            }

            element.dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
            element.dispatchEvent(new KeyboardEvent('keydown', { key: char, bubbles: true, cancelable: true }));
            element.dispatchEvent(new KeyboardEvent('keyup', { key: char, bubbles: true, cancelable: true }));
            
            await new Promise(resolve => setTimeout(resolve, 50)); // 每个字符输入延迟50ms
        }

        element.dispatchEvent(new Event('change', { bubbles: true, cancelable: true }));
        element.blur();
    }

    const HIGHLIGHT_CLASS = 'automation-highlight-element';
    const STYLE_INJECTED_MARKER = 'automation-style-injected';

    // 确保高亮样式被注入到指定的文档中
    function ensureHighlightStyle(doc) {
        if (doc && doc.head && !doc.documentElement.classList.contains(STYLE_INJECTED_MARKER)) {
            const style = doc.createElement('style');
            style.textContent = `
                .${HIGHLIGHT_CLASS} {
                    border: 2px solid red !important;
                    box-shadow: 0 0 10px red !important;
                    transition: all 0.2s ease-in-out !important;
                }
            `;
            doc.head.appendChild(style);
            doc.documentElement.classList.add(STYLE_INJECTED_MARKER);
        }
    }

    // 确保主文档有样式
    ensureHighlightStyle(document);

    function highlightElement(element, start) {
        if (start) {
            element.classList.add(HIGHLIGHT_CLASS);
        } else {
            element.classList.remove(HIGHLIGHT_CLASS);
        }
    }

    function clearAllHighlights() {
        // 确保在所有可能的文档上下文中清除高亮
        const docs = [document];
        // 简单地尝试查找所有iframe并清除，忽略跨域错误
        document.querySelectorAll('iframe').forEach(iframe => {
            try {
                if (iframe.contentDocument) {
                    docs.push(iframe.contentDocument);
                }
            } catch (e) {
                // 忽略跨域iframe
            }
        });

        docs.forEach(doc => {
            doc.querySelectorAll(`.${HIGHLIGHT_CLASS}`).forEach(el => {
                highlightElement(el, false);
            });
        });
    }


    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        if (sender.tab) {
            return true;
        }
        (async () => {
            const { command, selector, value, timeout = 0, stability = 0, clicks = 1, clickDelay = 100 } = request;
            
            try {
                let element = null;
                if (selector) {
                    element = await waitForElement(selector, timeout, stability);
                    if (!element) {
                        const selectorString = typeof selector === 'string' ? selector : JSON.stringify(selector);
                        throw new Error(`选择器为 ${selectorString} 的元素未找到。`);
                    }
                } else if (['click', 'type', 'scrape_text', 'scrape_html'].includes(command) && command !== 'scrape_html' && command !== 'switch_iframe') {
                    // scrape_html 可以没有选择器（抓取整个页面）
                    // switch_iframe 可以没有选择器（切换回主文档）
                    throw new Error(`${command} 命令需要 'selector' 参数。`);
                }

                let result = null;
                switch (command) {
                    case 'show_floating_ui':
                        console.log('收到显示悬浮UI命令');
                        if (window.approvalFloatingUI) {
                            console.log('悬浮UI实例存在，正在显示');
                            window.approvalFloatingUI.show();
                            sendResponse({ success: true });
                        } else {
                            console.log('悬浮UI未初始化，尝试重新初始化');
                            // 尝试重新初始化
                            await loadFloatingUIScript();
                            if (window.approvalFloatingUI) {
                                window.approvalFloatingUI.show();
                                sendResponse({ success: true });
                            } else {
                                sendResponse({ success: false, error: '悬浮UI初始化失败' });
                            }
                        }
                        break;
                    case 'hide_floating_ui':
                        console.log('收到隐藏悬浮UI命令');
                        if (window.approvalFloatingUI) {
                            window.approvalFloatingUI.hide();
                            sendResponse({ success: true });
                        } else {
                            sendResponse({ success: false, error: '悬浮UI未初始化' });
                        }
                        break;
                    case 'update_floating_ui':
                        if (window.approvalFloatingUI && window.approvalFloatingUI.isVisible) {
                            window.approvalFloatingUI.updateUI(request.statusInfo);
                            sendResponse({ success: true });
                        } else {
                            // 如果悬浮UI不可见，不需要更新
                            sendResponse({ success: true });
                        }
                        break;
                    case 'clear_highlights':
                        clearAllHighlights();
                        sendResponse({ success: true });
                        break;
                    case 'switch_iframe':
                        if (!selector) {
                            // 如果选择器为空，则切换回主文档
                            currentDocument = document;
                            ensureHighlightStyle(currentDocument); // 确保样式存在
                            sendResponse({ success: true, data: "已切换回主文档。" });
                        } else {
                            // 查找 iframe 时，必须在当前的上下文中查找
                            const iframeElement = await waitForElement(selector, timeout, stability);
                            if (!iframeElement || iframeElement.tagName.toLowerCase() !== 'iframe') {
                                const selectorString = typeof selector === 'string' ? selector : JSON.stringify(selector);
                                throw new Error(`选择器 ${selectorString} 没有找到 iframe 元素。`);
                            }
                            
                            // 高亮 iframe 元素本身，以提供视觉反馈
                            highlightElement(iframeElement, true);
                            await new Promise(resolve => setTimeout(resolve, 500)); // 保持高亮

                            // 检查同源策略
                            try {
                                if (iframeElement.contentDocument) {
                                    currentDocument = iframeElement.contentDocument;
                                    // 确保样式被注入到 iframe 的文档中
                                    ensureHighlightStyle(currentDocument);
                                    sendResponse({ success: true, data: `已切换到 iframe: ${JSON.stringify(selector)}` });
                                } else {
                                    throw new Error('无法访问 iframe 的 contentDocument。它可能尚未加载完成。');
                                }
                            } catch (e) {
                                throw new Error(`无法切换到 iframe：它可能是跨域的，或者存在安全限制。错误: ${e.message}`);
                            }
                        }
                        break;
                    case 'click':
                        highlightElement(element, true);
                        await new Promise(resolve => setTimeout(resolve, 500)); // 保持高亮0.5s
                        
                        if (clicks === 2) {
                            // 专门处理双击事件，以触发 dblclick 监听器
                            const dblClickEvent = new MouseEvent('dblclick', {
                                bubbles: true,
                                cancelable: true,
                                view: window,
                                detail: 2
                            });
                            element.dispatchEvent(dblClickEvent);
                        } else {
                            // 处理单击或多次（非2次）单击
                            for (let i = 0; i < clicks; i++) {
                                element.click();
                                if (i < clicks - 1) {
                                    await new Promise(resolve => setTimeout(resolve, clickDelay));
                                }
                            }
                        }
                        sendResponse({ success: true, data: null });
                        break;
                    case 'type':
                        // 高亮在 typeInElement 内部处理
                        await typeInElement(element, value);
                        sendResponse({ success: true, data: null });
                        break;
                    case 'scrape_text':
                        highlightElement(element, true);
                        await new Promise(resolve => setTimeout(resolve, 500)); // 保持高亮0.5s
                        result = element.innerText;
                        sendResponse({ success: true, data: result });
                        break;
                    case 'scrape_html':
                        if (element) {
                            highlightElement(element, true);
                            await new Promise(resolve => setTimeout(resolve, 500));
                            result = element.outerHTML;
                        } else {
                            // 如果没有选择器，则抓取当前上下文的HTML
                            result = currentDocument.documentElement.outerHTML;
                        }
                        sendResponse({ success: true, data: result });
                        break;
                    default:
                        // 对于不需要选择器的命令，可以在这里处理
                        sendResponse({ success: true, data: null });
                }

            } catch (error) {
                sendResponse({ success: false, error: error.message });
            }
        })();
        return true;
    });
}
