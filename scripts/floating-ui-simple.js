// 简化版悬浮UI - 用于调试
console.log('加载简化版悬浮UI');

class SimpleFloatingUI {
    constructor() {
        console.log('初始化简化版悬浮UI');
        this.container = null;
        this.isVisible = false;
        this.init();
    }
    
    init() {
        this.createContainer();
        this.attachEventListeners();
        this.setupMessageHandling();
        console.log('简化版悬浮UI初始化完成');
    }

    setupMessageHandling() {
        // 监听来自content script的消息
        document.addEventListener('backgroundMessage', (event) => {
            this.handleMessageFromBackground(event.detail);
        });

        console.log('简化版悬浮UI消息处理已设置');
    }
    
    createContainer() {
        // 移除已存在的容器
        const existing = document.getElementById('approval-automation-floating-ui');
        if (existing) {
            existing.remove();
        }
        
        // 创建主容器
        this.container = document.createElement('div');
        this.container.id = 'approval-automation-floating-ui';
        this.container.style.cssText = `
            position: fixed !important;
            top: 20px !important;
            right: 20px !important;
            width: 300px !important;
            background: white !important;
            border: 2px solid #007bff !important;
            border-radius: 8px !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
            font-family: Arial, sans-serif !important;
            font-size: 14px !important;
            z-index: 2147483647 !important;
            color: #333 !important;
            display: none !important;
        `;
        
        // 创建HTML结构
        this.container.innerHTML = `
            <div style="background: #007bff; color: white; padding: 12px; display: flex; justify-content: space-between; align-items: center; cursor: grab;">
                <span style="font-weight: bold;">审批自动化</span>
                <div>
                    <button id="simple-minimize-btn" style="background: rgba(255,255,255,0.2); border: none; color: white; width: 24px; height: 24px; margin-right: 5px; cursor: pointer;">−</button>
                    <button id="simple-close-btn" style="background: rgba(255,255,255,0.2); border: none; color: white; width: 24px; height: 24px; cursor: pointer;">×</button>
                </div>
            </div>
            <div id="simple-content" style="padding: 16px;">
                <div style="margin-bottom: 10px;">
                    <strong>状态：</strong><span id="simple-status">空闲</span>
                </div>
                <div style="margin-bottom: 10px;">
                    <strong>当前步骤：</strong><span id="simple-step">0</span>
                </div>
                <div style="margin-bottom: 10px;">
                    <button id="simple-start-btn" style="background: #28a745; color: white; border: none; padding: 8px 16px; margin-right: 5px; cursor: pointer;">启动</button>
                    <button id="simple-stop-btn" style="background: #dc3545; color: white; border: none; padding: 8px 16px; cursor: pointer;">停止</button>
                </div>
                <div id="simple-debug" style="font-size: 12px; color: #666; border-top: 1px solid #eee; padding-top: 10px; margin-top: 10px;">
                    调试信息将显示在这里
                </div>
            </div>
        `;
        
        // 添加到页面
        document.body.appendChild(this.container);
        console.log('简化版悬浮UI容器已创建');
    }
    
    attachEventListeners() {
        // 关闭按钮
        const closeBtn = this.container.querySelector('#simple-close-btn');
        closeBtn.addEventListener('click', () => {
            console.log('点击关闭按钮');
            this.hide();
        });
        
        // 最小化按钮
        const minimizeBtn = this.container.querySelector('#simple-minimize-btn');
        const content = this.container.querySelector('#simple-content');
        let isMinimized = false;
        
        minimizeBtn.addEventListener('click', () => {
            isMinimized = !isMinimized;
            if (isMinimized) {
                content.style.display = 'none';
                minimizeBtn.textContent = '+';
            } else {
                content.style.display = 'block';
                minimizeBtn.textContent = '−';
            }
        });
        
        // 控制按钮
        const startBtn = this.container.querySelector('#simple-start-btn');
        const stopBtn = this.container.querySelector('#simple-stop-btn');
        
        startBtn.addEventListener('click', () => {
            console.log('点击启动按钮');
            this.updateDebug('发送启动命令...');
            this.sendMessageToBackground({
                command: 'start',
                isAutoApprove: true
            });
        });

        stopBtn.addEventListener('click', () => {
            console.log('点击停止按钮');
            this.updateDebug('发送停止命令...');
            this.sendMessageToBackground({ command: 'stop' });
        });
        
        console.log('简化版悬浮UI事件监听器已附加');
    }
    
    show() {
        console.log('显示简化版悬浮UI');
        this.isVisible = true;
        this.container.style.display = 'block';
        this.updateDebug('悬浮UI已显示');

        // 请求当前状态
        this.sendMessageToBackground({ command: 'getStatus' });
    }

    // 通过content script发送消息到background script
    sendMessageToBackground(message) {
        console.log('简化版悬浮UI发送消息:', message);
        this.updateDebug('发送消息: ' + message.command);

        // 发送自定义事件到content script
        const event = new CustomEvent('floatingUIMessage', {
            detail: message
        });
        document.dispatchEvent(event);
    }

    // 接收来自content script的消息
    handleMessageFromBackground(message) {
        console.log('简化版悬浮UI收到消息:', message);
        this.updateDebug('收到响应: ' + JSON.stringify(message).substring(0, 100));

        if (message.type === 'statusUpdate' || message.command === 'getStatus') {
            this.updateStatus(message);
        }
    }
    
    hide() {
        console.log('隐藏简化版悬浮UI');
        this.isVisible = false;
        this.container.style.display = 'none';
        this.updateDebug('悬浮UI已隐藏');
    }
    
    updateStatus(statusInfo) {
        console.log('更新状态:', statusInfo);
        
        const statusSpan = this.container.querySelector('#simple-status');
        const stepSpan = this.container.querySelector('#simple-step');
        
        if (statusSpan) {
            statusSpan.textContent = statusInfo.state || '未知';
        }
        
        if (stepSpan) {
            stepSpan.textContent = statusInfo.currentStep || 0;
        }
        
        this.updateDebug('状态已更新: ' + (statusInfo.state || '未知'));
    }
    
    updateUI(statusInfo) {
        this.updateStatus(statusInfo);
    }
    
    updateDebug(message) {
        const debugDiv = this.container.querySelector('#simple-debug');
        if (debugDiv) {
            const timestamp = new Date().toLocaleTimeString();
            debugDiv.innerHTML = `[${timestamp}] ${message}<br>` + debugDiv.innerHTML;
            
            // 限制调试信息的行数
            const lines = debugDiv.innerHTML.split('<br>');
            if (lines.length > 10) {
                debugDiv.innerHTML = lines.slice(0, 10).join('<br>');
            }
        }
    }
}

// 导出到全局
window.SimpleFloatingUI = SimpleFloatingUI;
console.log('SimpleFloatingUI类已导出到全局');
