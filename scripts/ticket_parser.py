from bs4 import BeautifulSoup, Tag
import json
import re
from datetime import datetime

class TicketParser:
    def __init__(self):
        self.data = {}
        self.errors = []
        self.soup = None
        self.base_year = datetime.now().year
        self.last_known_month = None

    def _add_error(self, message, field_name=""):
        """记录一条解析错误"""
        self.errors.append(f"解析错误{'在字段 ' + field_name if field_name else ''}: {message}")

    def _get_text(self, element: Tag, field_name: str):
        """安全地获取元素文本"""
        if not isinstance(element, Tag):
            self._add_error(f"元素无效", field_name)
            return ""
        return re.sub(r'[\s\xa0&nbsp;]+', ' ', element.get_text()).strip()

    def _find_value_by_label(self, label_regex: str, parent_tag: Tag = None, field_name: str = ""):
        """在指定容器内，通过标签文本查找并返回对应的值"""
        source = parent_tag or self.soup
        if not isinstance(source, Tag):
            self._add_error(f"用于查找的父标签无效", field_name)
            return ""
            
        label_cell = source.find('td', string=re.compile(label_regex))
        if not label_cell:
            self._add_error(f"未找到标签 '{label_regex}'", field_name)
            return ""
        
        value_cell = label_cell.find_next_sibling('td')
        if not value_cell:
            self._add_error(f"找到了标签 '{label_regex}' 但未找到其对应的值单元格", field_name)
            return ""
        
        value = self._get_text(value_cell, field_name)
        # 循环查找直到找到有内容的单元格或没有更多单元格
        while not value and value_cell.find_next_sibling('td'):
            value_cell = value_cell.find_next_sibling('td')
            value = self._get_text(value_cell, field_name)
            
        return value

    def _parse_datetime(self, text: str, field_name: str):
        """健壮的时间解析，增加跨年处理"""
        if not text:
            return "" # 不记录错误，因为上游已经处理
        
        clean_text = text.replace('年', '-').replace('月', '-').replace('日', ' ').replace('时', ':').replace('分', '').replace('：', ':')
        clean_text = re.sub(r'\s+', ' ', clean_text).strip()
        clean_text = re.sub(r'\s*([:-])\s*', r'\1', clean_text)

        formats_to_try = ["%Y-%m-%d %H:%M", "%m-%d %H:%M"]
        for fmt in formats_to_try:
            try:
                dt = datetime.strptime(clean_text, fmt)
                year_to_use = self.base_year
                
                if fmt == "%m-%d %H:%M":
                    # 跨年处理：如果当前解析的月份小于上一个已知月份（例如从12月到1月），则年份加一
                    if self.last_known_month and dt.month < self.last_known_month:
                        year_to_use += 1
                    dt = dt.replace(year=year_to_use)

                # 更新状态以供下一次解析使用
                self.last_known_month = dt.month
                # 如果解析出了完整的年份，它将成为新的基准年
                if fmt == "%Y-%m-%d %H:%M":
                    self.base_year = dt.year
                    
                return dt.strftime("%Y-%m-%d %H:%M")
            except ValueError:
                continue
        
        # 如果所有格式都解析失败，检查是否是模板占位符
        if not any(char.isdigit() for char in clean_text):
            return "" # 是模板，返回空字符串，不报错

        self._add_error(f"时间格式无法识别 '{text}'", field_name)
        return clean_text

    def parse(self, html_content: str):
        """主解析方法"""
        if not html_content:
            self._add_error("输入的HTML内容为空")
            return {}, self.errors

        self.soup = BeautifulSoup(html_content, 'html.parser')
        
        self._set_base_year()

        # 按顺序执行所有提取函数
        self.extract_basic_info()
        self.extract_time_info()
        self.extract_team_info()
        self.extract_main_sections()
        self.extract_safety_measures()
        self.extract_signature_sections()
        
        return self.data, self.errors

    def _set_base_year(self):
        start_time_label = self.soup.find('td', string='自')
        if start_time_label:
            start_time_cell = start_time_label.find_next_sibling('td')
            if start_time_cell:
                start_time_str = self._get_text(start_time_cell, "计划开始时间")
                year_match = re.search(r'(\d{4})\s*年', start_time_str)
                if year_match:
                    self.base_year = int(year_match.group(1))
                
                # 提取月份作为跨年判断的初始依据
                month_match = re.search(r'(\d{1,2})\s*月', start_time_str)
                if month_match:
                    self.last_known_month = int(month_match.group(1))

    def extract_basic_info(self):
        type_el = self.soup.find('td', class_=re.compile('style1'))
        self.data['工作票类型'] = self._get_text(type_el, '工作票类型') if type_el else self._add_error("未找到工作票类型", "工作票类型")
        self.data['编号'] = self._find_value_by_label(r'^编号[：:]', field_name='编号')
        self.data['工作负责人（监护人）'] = self._find_value_by_label(r'工作负责人[（(]监护人[)）][：:]', field_name='工作负责人')
        unit_and_team_str = self._find_value_by_label(r'^单位和班组[：:]', field_name='单位和班组')
        if unit_and_team_str:
            self.data['单位和班组'] = [item.strip() for item in re.split(r'[,，、]', unit_and_team_str) if item.strip()]
        else:
            self.data['单位和班组'] = []
        self.data['工作负责人及工作班人员总数'] = self._find_value_by_label(r'工作负责人及工作班人员总数共', field_name='工作班总数')

    def extract_time_info(self):
        start_td = self.soup.find('td', string='自')
        end_td = self.soup.find('td', string='至')
        start_time_str, end_time_str = "", ""
        if start_td and start_td.find_next_sibling('td'):
            start_time_str = self._get_text(start_td.find_next_sibling('td'), "计划开始时间")
        else:
            self._add_error("未找到计划开始时间", "计划工作时间")
        if end_td and end_td.find_next_sibling('td'):
            end_time_str = self._get_text(end_td.find_next_sibling('td'), "计划结束时间")
        else:
            self._add_error("未找到计划结束时间", "计划工作时间")
        self.data['计划工作时间'] = {
            '开始时间': self._parse_datetime(start_time_str, "计划开始时间"),
            '结束时间': self._parse_datetime(end_time_str, "计划结束时间")
        }

    def extract_team_info(self):
        members_header = self.soup.find('td', string=re.compile(r'工作班人员[（(]不包括工作负责人[)）][：:]'))
        if members_header:
            members_tr = members_header.find_parent('tr')
            if members_tr and members_tr.find_next_sibling('tr'):
                content_cell = members_tr.find_next_sibling('tr').find('td')
                if content_cell:
                    raw_text = self._get_text(content_cell, "工作班人员")
                    members_list = []
                    pattern = re.compile(r'(.+?)\s*[（(](.+?)[)）]')
                    # 使用正则表达式支持多种分隔符
                    for part in re.split(r'[,，、]', raw_text):
                        cleaned_part = part.strip()
                        if cleaned_part:
                            match = pattern.match(cleaned_part)
                            if match:
                                members_list.append({'姓名': match.group(1).strip(), '单位': match.group(2).strip()})
                            else:
                                members_list.append({'姓名': cleaned_part, '单位': None})
                    self.data['工作班人员（不包括工作负责人）'] = members_list
                else: self._add_error("未找到工作班人员内容单元格", "工作班人员")
            else: self._add_error("未找到工作班人员内容行", "工作班人员")
        else: self._add_error("未找到工作班人员标题", "工作班人员")

    def extract_main_sections(self):
        task_header = self.soup.find('td', string=re.compile(r'^工作任务[：:]'))
        if task_header:
            task_tr = task_header.find_parent('tr')
            if task_tr:
                next_tr = task_tr.find_next_sibling('tr')
                if next_tr:
                    task_cell = next_tr.td
                    if task_cell:
                        for br in task_cell.find_all("br"): br.replace_with("\n")
                        self.data['工作任务'] = self._get_text(task_cell, "工作任务")
                    else:
                        self._add_error("未找到工作任务单元格", "工作任务")
                else:
                    self._add_error("未找到工作任务内容行", "工作任务")
            else:
                self._add_error("未找到工作任务的父行", "工作任务")
        else:
            self._add_error("未找到工作任务部分", "工作任务")

        self.data['工作地点'] = self._find_value_by_label(r'^工作地点[：:]', field_name='工作地点')

        impact_header = self.soup.find('td', string=re.compile(r'^工作影响范围[：:]'))
        if impact_header:
            impact_tr = impact_header.find_parent('tr')
            if impact_tr:
                next_tr = impact_tr.find_next_sibling('tr')
                if next_tr:
                    impact_cell = next_tr.td
                    if impact_cell:
                        self.data['工作影响范围'] = self._get_text(impact_cell, "工作影响范围")
                    else:
                        self._add_error("未找到工作影响范围单元格", "工作影响范围")
                else:
                    self._add_error("未找到工作影响范围内容行", "工作影响范围")
            else:
                self._add_error("未找到工作影响范围父行", "工作影响范围")
        else:
            self._add_error("未找到工作影响范围部分", "工作影响范围")

    def extract_safety_measures(self):
        measures = {}
        header = self.soup.find('td', string=re.compile(r'工作要求的安全措施[：:]'))
        if header and header.find_parent('tr'):
            current_tr = header.find_parent('tr')
            # 硬件及工作环境
            hw_header = current_tr.find('td', string=re.compile(r'硬件及工作环境,应设遮栏、应挂标示牌位置'))
            if hw_header and current_tr.find_next_sibling('tr'):
                current_tr = current_tr.find_next_sibling('tr')
                hw_content_cell = current_tr.find('td')
                if hw_content_cell:
                    for br in hw_content_cell.find_all("br"): br.replace_with("\n")
                    measures['硬件及工作环境,应设遮栏、应挂标示牌位置'] = self._get_text(hw_content_cell, '安全措施-硬件')
            
            # 软件及数据
            if current_tr and current_tr.find_next_sibling('tr'):
                current_tr = current_tr.find_next_sibling('tr')
                sw_header = current_tr.find('td', string=re.compile(r'软件及数据'))
                if sw_header and current_tr.find_next_sibling('tr'):
                    current_tr = current_tr.find_next_sibling('tr')
                    sw_content_cell = current_tr.find('td')
                    if sw_content_cell:
                        measures['软件及数据'] = self._get_text(sw_content_cell, '安全措施-软件')

            # 附件
            if current_tr and current_tr.find_next_sibling('tr'):
                current_tr = current_tr.find_next_sibling('tr')
                attachment_label = current_tr.find('td', string=re.compile(r'^附件[：:]'))
                if attachment_label and attachment_label.find_next_sibling('td'):
                    measures['附件'] = self._get_text(attachment_label.find_next_sibling('td'), '附件')
                else:
                    measures['附件'] = ""
        else: 
            self._add_error("未找到'工作要求的安全措施'部分")
        
        self.data['工作要求的安全措施'] = measures

    def extract_signature_sections(self):
        """提取所有签名、许可、终结等部分"""
        self._extract_issuance_section()
        self._extract_permit_section()
        self._extract_safety_briefing_section()
        self._extract_interruption_section()
        self._extract_change_section()
        self._extract_extension_section()
        self._extract_termination_section()
        self._extract_notes_section()

    def _safe_get_value_from_label_in_row(self, row, label_text, field_name):
        """在一行内安全地查找标签并获取其值"""
        if not row: return ""
        label = row.find('td', string=re.compile(label_text))
        if label and label.find_next_sibling('td'):
            return self._get_text(label.find_next_sibling('td'), field_name)
        return ""

    def _extract_issuance_section(self):
        section = {}
        issuer_label = self.soup.find('td', string=re.compile(r'工作票签发人签名[：:]'))
        if issuer_label and issuer_label.find_parent('tr'):
            issuer_row = issuer_label.find_parent('tr')
            section['工作票签发人签名'] = self._safe_get_value_from_label_in_row(issuer_row, r'工作票签发人签名[：:]', '签发人签名')
            time_val = self._safe_get_value_from_label_in_row(issuer_row, r'时间[：:]', '签发时间')
            section['工作票签发时间'] = self._parse_datetime(time_val, '签发时间')

        countersign_label = self.soup.find('td', string=re.compile(r'工作票会签人签名[：:]'))
        if countersign_label and countersign_label.find_parent('tr'):
            countersign_row = countersign_label.find_parent('tr')
            section['工作票会签人签名'] = self._safe_get_value_from_label_in_row(countersign_row, r'工作票会签人签名[：:]', '会签人签名')
            time_val = self._safe_get_value_from_label_in_row(countersign_row, r'时间[：:]', '会签时间')
            section['工作票会签时间'] = self._parse_datetime(time_val, '会签时间')
        self.data['签发'] = section

    def _extract_permit_section(self):
        section = {}
        header = self.soup.find('td', string=re.compile(r'^工作许可$'))
        if header and header.find_parent('tr'):
            row = header.find_parent('tr')
            section['安全措施是否满足工作要求'] = self._safe_get_value_from_label_in_row(row, r'安全措施是否满足工作要求[：:]', '安全措施满足要求')
            
            row = row.find_next_sibling('tr')
            if row: section['需补充或调整的安全措施'] = self._safe_get_value_from_label_in_row(row, r'需补充或调整的安全措施[：:]', '补充措施')
            
            row = row.find_next_sibling('tr') if row else None
            if row: section['其他安全注意事项'] = self._safe_get_value_from_label_in_row(row, r'其他安全注意事项[：:]', '其他注意事项')

            row = row.find_next_sibling('tr') if row else None
            if row:
                section['工作许可人签名'] = self._safe_get_value_from_label_in_row(row, r'工作许可人签名[：:]', '许可人签名')
                section['工作负责人签名'] = self._safe_get_value_from_label_in_row(row, r'工作负责人签名[：:]', '负责人签名')

            row = row.find_next_sibling('tr') if row else None
            if row:
                time_val = self._safe_get_value_from_label_in_row(row, r'时间[：:]', '许可时间')
                section['许可时间'] = self._parse_datetime(time_val, '许可时间')
        else: self._add_error("未找到'工作许可'部分")
        self.data['工作许可'] = section

    def _extract_safety_briefing_section(self):
        section = {}
        header = self.soup.find('td', string=re.compile(r'^安全交代$'))
        if header and header.find_parent('tr'):
            header_row = header.find_parent('tr')
            
            # 提取说明
            desc_cell = header.find_next_sibling('td')
            if desc_cell:
                section['说明'] = self._get_text(desc_cell, '安全交代说明')
            else:
                section['说明'] = ""

            # 签名行是头部的下下个兄弟
            signature_row = header_row.find_next_sibling('tr')
            if signature_row:
                signature_row = signature_row.find_next_sibling('tr')

            if signature_row:
                signatures_text = self._safe_get_value_from_label_in_row(signature_row, r'工作班人员签名[：:]', '班组成员签名')
                signatures_list = []
                pattern = re.compile(r'(.+?)\s*[（(](.+?)[)）]')
                # 使用正则表达式支持多种分隔符
                for part in re.split(r'[,，、]', signatures_text):
                    cleaned_part = part.strip()
                    if cleaned_part:
                        match = pattern.match(cleaned_part)
                        if match:
                            signatures_list.append({'姓名': match.group(1).strip(), '单位': match.group(2).strip()})
                        else:
                            signatures_list.append({'姓名': cleaned_part, '单位': None})
                section['工作班人员签名'] = signatures_list
                time_val = self._safe_get_value_from_label_in_row(signature_row, r'时间[：:]', '交代时间')
                section['交代时间'] = self._parse_datetime(time_val, '交代时间')
            else:
                section['工作班人员签名'] = []
                section['交代时间'] = ""
        else:
            self._add_error("未找到'安全交代'部分")
        self.data['安全交代'] = section

    def _extract_interruption_section(self):
        section = []
        header = self.soup.find('td', string=re.compile(r'^工作间断$'))
        if header and header.find_parent('tr'):
            current_row = header.find_parent('tr').find_next_sibling('tr')
            while current_row:
                cells = current_row.find_all('td')
                if not cells or (cells[0].get('rowspan') and cells[0].find(string=re.compile(r'工作变更|工作延期|工作票终结'))):
                    break
                if len(cells) >= 6:
                    time_str = self._get_text(cells[0], '')
                    if time_str and "月" not in time_str:
                        entry = {
                            '工作间断时间': self._parse_datetime(self._get_text(cells[0], '间断时间'), '间断时间'),
                            '工作许可人（间断）': self._get_text(cells[1], '许可人（间断）'),
                            '工作负责人（间断）': self._get_text(cells[2], '负责人（间断）'),
                            '工作开工时间': self._parse_datetime(self._get_text(cells[3], '开工时间'), '开工时间'),
                            '工作许可人（开工）': self._get_text(cells[4], '许可人（开工）'),
                            '工作负责人（开工）': self._get_text(cells[5], '负责人（开工）'),
                        }
                        section.append(entry)
                current_row = current_row.find_next_sibling('tr')
        else: self._add_error("未找到'工作间断'部分")
        self.data['工作间断'] = section

    def _extract_change_section(self):
        section = {}
        header = self.soup.find('td', string=re.compile(r'^工作变更$'))
        if not header:
            self._add_error("未找到'工作变更'部分")
            return
        
        # 子方法负责查找和填充，如果找不到会返回空字典
        section['工作任务变更'] = self._extract_task_change_sub(header)
        section['工作负责人变更'] = self._extract_owner_change_sub(header)
        section['工作班成员变更'] = self._extract_team_change_sub(header)
        self.data['工作变更'] = section

    def _extract_task_change_sub(self, start_node):
        data = {}
        label = start_node.find_next('td', string=re.compile(r'不需变更安全措施下增加的工作内容[：:]'))
        if label and label.find_parent('tr'):
            row = label.find_parent('tr').find_next_sibling('tr')
            if row: data['增加的工作内容'] = self._get_text(row.find('td'), '增加的工作内容')
            row = row.find_next_sibling('tr') if row else None
            if row:
                data['工作许可人签名'] = self._safe_get_value_from_label_in_row(row, r'工作许可人签名', '增加内容-许可人')
                data['工作负责人签名'] = self._safe_get_value_from_label_in_row(row, r'工作负责人签名', '增加内容-负责人')
            row = row.find_next_sibling('tr') if row else None
            if row:
                time_val = self._safe_get_value_from_label_in_row(row, r'时间', '增加内容-时间')
                data['时间'] = self._parse_datetime(time_val, '增加内容-时间')
        return data

    def _extract_owner_change_sub(self, start_node):
        data = {}
        label = start_node.find_next('td', string=re.compile(r'原工作负责人签名[：:]'))
        if label and label.find_parent('tr'):
            row2 = label.find_parent('tr')
            row1 = row2.find_previous_sibling('tr')
            row3 = row2.find_next_sibling('tr')
            if row1:
                data['工作票签发人签名'] = self._safe_get_value_from_label_in_row(row1, r'工作票签发人签名', '负责人变更-签发人')
                time_val1 = self._safe_get_value_from_label_in_row(row1, r'同意变更时间', '负责人变更-同意时间')
                data['同意变更时间'] = self._parse_datetime(time_val1, '负责人变更-同意时间')
            if row2:
                data['原工作负责人签名'] = self._safe_get_value_from_label_in_row(row2, r'原工作负责人签名', '负责人变更-原负责人')
                data['现工作负责人签名'] = self._safe_get_value_from_label_in_row(row2, r'现工作负责人签名', '负责人变更-现负责人')
            if row3:
                data['工作许可人签名'] = self._safe_get_value_from_label_in_row(row3, r'工作许可人签名', '负责人变更-许可人')
                time_val2 = self._safe_get_value_from_label_in_row(row3, r'时间', '负责人变更-许可时间')
                data['许可时间'] = self._parse_datetime(time_val2, '负责人变更-许可时间')
        return data

    def _extract_team_change_sub(self, start_node):
        data = []
        label = start_node.find_next('td', string=re.compile(r'变更情况'))
        if label and label.find_parent('tr'):
            row = label.find_parent('tr').find_next_sibling('tr')
            while row:
                cells = row.find_all('td')
                if not cells or (cells[0].get('rowspan')): break
                if len(cells) >= 5 and '月' not in self._get_text(cells[4], ''):
                    entry = {
                        '变更情况': self._get_text(cells[0], '成员变更-情况'),
                        '工作许可人': self._get_text(cells[1], '成员变更-许可人'),
                        '工作负责人': self._get_text(cells[2], '成员变更-负责人'),
                        '变更时间': self._parse_datetime(self._get_text(cells[3], '成员变更-时间'), '成员变更-时间')
                    }
                    data.append(entry)
                row = row.find_next_sibling('tr')
        return data

    def _extract_extension_section(self):
        section = {}
        header = self.soup.find('td', string=re.compile(r'^工作延期$'))
        if header and header.find_parent('tr'):
            row = header.find_parent('tr')
            val = self._safe_get_value_from_label_in_row(row, r'有效期延长到[：:]', '有效期')
            section['有效期延长到'] = self._parse_datetime(val, '有效期')
            row = row.find_next_sibling('tr')
            if row:
                section['工作许可人签名'] = self._safe_get_value_from_label_in_row(row, r'工作许可人签名[：:]', '延期-许可人')
                section['工作负责人签名'] = self._safe_get_value_from_label_in_row(row, r'工作负责人签名[：:]', '延期-负责人')
            row = row.find_next_sibling('tr') if row else None
            if row:
                time_val = self._safe_get_value_from_label_in_row(row, r'时间[：:]', '延期时间')
                section['延期时间'] = self._parse_datetime(time_val, '延期时间')
        else: self._add_error("未找到'工作延期'部分")
        self.data['工作延期'] = section

    def _extract_termination_section(self):
        section = {}
        header = self.soup.find('td', string=re.compile(r'^工作票终结$'))
        if header and header.find_parent('tr'):
            row = header.find_parent('tr')
            desc_cell1 = row.find('td', string=re.compile(r'全部作业于'))
            if desc_cell1:
                desc1 = self._get_text(desc_cell1, '终结描述1')
                desc_cell2 = desc_cell1.find_next_sibling('td')
                desc2 = self._get_text(desc_cell2, '终结描述2') if desc_cell2 else ''
                desc_cell3 = desc_cell2.find_next_sibling('td') if desc_cell2 else None
                desc3 = self._get_text(desc_cell3, '终结描述3') if desc_cell3 else ''
                next_row = row.find_next_sibling('tr')
                desc4 = self._get_text(next_row.find('td'), '终结描述4') if next_row else ''
                section['描述'] = f"{desc1} {desc2} {desc3} {desc4}".strip()
            
            next_row = row.find_next_sibling('tr')
            signature_row = next_row.find_next_sibling('tr') if next_row else None
            if signature_row:
                section['工作负责人签名'] = self._safe_get_value_from_label_in_row(signature_row, r'工作负责人签名[：:]', '终结-负责人')
                section['工作许可人签名'] = self._safe_get_value_from_label_in_row(signature_row, r'工作许可人签名[：:]', '终结-许可人')
        else: self._add_error("未找到'工作票终结'部分")
        self.data['工作票终结'] = section

    def _extract_notes_section(self):
        header = self.soup.find('td', string=re.compile(r'备注[（(].*[)）]'))
        if header and header.find_parent('tr'):
            content_tr = header.find_parent('tr').find_next_sibling('tr')
            if content_tr:
                self.data['备注'] = self._get_text(content_tr.find('td'), '备注')
            else: self.data['备注'] = ""
        else: self._add_error("未找到'备注'部分")

    def to_json(self):
        return json.dumps(self.data, ensure_ascii=False, indent=2)

if __name__ == "__main__":
    # 用于调试的入口点
    try:
        with open(rf'data\html\txgzp\txgzp10.html', 'r', encoding='utf-8') as f:
            html = f.read()
            
        parser = TicketParser()
        data, errors = parser.parse(html)
        
        print("--- 解析数据 ---")
        print(json.dumps(data, ensure_ascii=False, indent=2))
        
        print("\n--- 解析错误 ---")
        if errors:
            for error in errors:
                print(error)
        else:
            print("无解析错误。")
    except FileNotFoundError:
        print("错误：找不到测试文件")
    except Exception as e:
        print(f"执行调试时发生错误: {e}")
