// 悬浮UI组件管理器
class FloatingUI {
    constructor() {
        this.container = null;
        this.isMinimized = false;
        this.currentStatusInfo = {};
        this.activeDetailVariable = null;
        this.isVisible = false;
        
        // SVG 图标
        this.icons = {
            pending: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-circle" viewBox="0 0 16 16"><path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/></svg>`,
            completed: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle-fill" viewBox="0 0 16 16"><path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/></svg>`,
            running: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-right-circle-fill" viewBox="0 0 16 16"><path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0zM4.5 7.5a.5.5 0 0 0 0 1h5.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H4.5z"/></svg>`,
            waiting: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-pause-circle-fill" viewBox="0 0 16 16"><path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM6.25 5C5.56 5 5 5.56 5 6.25v3.5a1.25 1.25 0 1 0 2.5 0v-3.5C7.5 5.56 6.94 5 6.25 5zm3.5 0c-.69 0-1.25.56-1.25 1.25v3.5a1.25 1.25 0 1 0 2.5 0v-3.5C11 5.56 10.44 5 9.75 5z"/></svg>`,
            waiting_user: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-person-fill" viewBox="0 0 16 16"><path d="M3 14s-1 0-1-1 1-4 6-4 6 3 6 4-1 1-1 1H3zm5-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/></svg>`,
            paused: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-pause-circle-fill" viewBox="0 0 16 16"><path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM6.25 5C5.56 5 5 5.56 5 6.25v3.5a1.25 1.25 0 1 0 2.5 0v-3.5C7.5 5.56 6.94 5 6.25 5zm3.5 0c-.69 0-1.25.56-1.25 1.25v3.5a1.25 1.25 0 1 0 2.5 0v-3.5C11 5.56 10.44 5 9.75 5z"/></svg>`,
            error: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-exclamation-circle-fill" viewBox="0 0 16 16"><path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8 4a.905.905 0 0 0-.9.995l.35 3.507a.552.552 0 0 0 1.1 0l.35-3.507A.905.905 0 0 0 8 4zm.002 6a1 1 0 1 0 0 2 1 1 0 0 0 0-2z"/></svg>`,
            warning: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-exclamation-triangle-fill" viewBox="0 0 16 16"><path d="M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/></svg>`
        };
        
        this.init();
    }
    
    init() {
        this.createContainer();
        this.attachEventListeners();
        this.setupMessageHandling();
    }

    setupMessageHandling() {
        // 监听来自content script的消息
        document.addEventListener('backgroundMessage', (event) => {
            this.handleMessageFromBackground(event.detail);
        });

        console.log('悬浮UI消息处理已设置');
    }
    
    createContainer() {
        // 创建主容器
        this.container = document.createElement('div');
        this.container.id = 'approval-automation-floating-ui';
        this.container.className = 'approval-floating-ui';
        
        // 创建HTML结构
        this.container.innerHTML = this.getUIHTML();
        
        // 添加到页面
        document.body.appendChild(this.container);
        
        // 默认隐藏
        this.hide();
    }
    
    getUIHTML() {
        return `
            <div class="floating-ui-header">
                <div class="floating-ui-title">
                    <span>审批自动化</span>
                    <div class="floating-ui-status" id="floating-status-indicator"></div>
                </div>
                <div class="floating-ui-controls">
                    <button id="floating-minimize-btn" class="floating-btn-icon" title="最小化">−</button>
                    <button id="floating-close-btn" class="floating-btn-icon" title="关闭">×</button>
                </div>
            </div>
            <div class="floating-ui-content" id="floating-ui-content">
                <div id="floating-variables-container" class="floating-section" style="display: none;">
                    <div class="floating-section-header">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-braces" viewBox="0 0 16 16">
                            <path d="M2.114 8.063V7.9c1.005-.102 1.497-.615 1.497-1.6V4.503c0-1.094.39-1.538 1.354-1.538h.273V2h-.376C3.25 2 2.49 2.759 2.49 4.352v1.524c0 1.062-.39 1.558-1.354 1.558h-.273v1.12h.376c1.005 0 1.497.492 1.497 1.6v1.524c0 1.593.76 2.352 2.372 2.352h.376v-.99H4.846c-.964 0-1.354-.444-1.354-1.538V9.663c0-.984-.492-1.497-1.497-1.6zM13.886 7.9v.163c-1.005.103-1.497.616-1.497 1.6v1.524c0 1.094-.39 1.538-1.354 1.538h-.273v.99h.376c1.613 0 2.372-.759 2.372-2.352v-1.524c0-1.062.39-1.558 1.354-1.558h.273v-1.12h-.376c-1.005 0-1.497-.492-1.497-1.6V4.503c0-1.593-.76-2.352-2.372-2.352h-.376v.99h.273c.964 0 1.354.444 1.354 1.538v1.524c0 .984.492 1.497 1.497 1.6z"/>
                        </svg>
                        <span>当前变量</span>
                    </div>
                    <table id="floating-variables-table" class="floating-table">
                        <thead>
                            <tr>
                                <th>变量名</th>
                                <th>类型</th>
                                <th>值</th>
                            </tr>
                        </thead>
                        <tbody id="floating-variables-body">
                        </tbody>
                    </table>
                </div>
                
                <div id="floating-warnings-container" class="floating-section floating-warning" style="display: none;">
                    <div class="floating-section-header">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-exclamation-triangle-fill" viewBox="0 0 16 16">
                            <path d="M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/>
                        </svg>
                        <span>执行警告</span>
                    </div>
                    <ul id="floating-warnings-list" class="floating-list"></ul>
                </div>
                
                <div id="floating-error-container" class="floating-section floating-error" style="display: none;">
                    <div class="floating-section-header">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                            <path d="M7.002 11a1 1 0 1 1 2 0 1 1 0 0 1-2 0zM7.1 4.995a.905.905 0 1 1 1.8 0l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 4.995z"/>
                        </svg>
                        <span>执行错误</span>
                    </div>
                    <div id="floating-error-message" class="floating-error-message"></div>
                    <div id="floating-error-details" class="floating-error-details"></div>
                </div>
                
                <div id="floating-completion-container" class="floating-section floating-success" style="display: none;">
                    <div class="floating-section-header">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle-fill" viewBox="0 0 16 16">
                            <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
                        </svg>
                        <span>任务状态</span>
                    </div>
                    <div id="floating-completion-content" class="floating-completion-content"></div>
                </div>
                
                <div id="floating-task-list-container" class="floating-section">
                    <div class="floating-section-header">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-list-task" viewBox="0 0 16 16">
                            <path fill-rule="evenodd" d="M2 2.5a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5V3a.5.5 0 0 0-.5-.5H2zM3 3H2v1h1V3z"/>
                            <path d="M5 3.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5zM5.5 7a.5.5 0 0 0 0 1h9a.5.5 0 0 0 0-1h-9zm0 4a.5.5 0 0 0 0 1h9a.5.5 0 0 0 0-1h-9z"/>
                            <path fill-rule="evenodd" d="M1.5 7a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5H2a.5.5 0 0 1-.5-.5V7zM2 7h1v1H2V7zm0 3.5a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5H2zm1 .5H2v1h1v-1z"/>
                        </svg>
                        <span>任务列表</span>
                    </div>
                    <div id="floating-task-list" class="floating-task-list"></div>
                </div>
                
                <div class="floating-controls">
                    <button id="floating-start-btn" class="floating-btn floating-btn-primary">启动</button>
                    <button id="floating-pause-btn" class="floating-btn" disabled>暂停</button>
                    <button id="floating-stop-btn" class="floating-btn" disabled>终止</button>
                    <button id="floating-next-step-btn" class="floating-btn" disabled>下一步</button>
                </div>
            </div>
        `;
    }
    
    attachEventListeners() {
        // 最小化/展开按钮
        const minimizeBtn = this.container.querySelector('#floating-minimize-btn');
        minimizeBtn.addEventListener('click', () => this.toggleMinimize());
        
        // 关闭按钮
        const closeBtn = this.container.querySelector('#floating-close-btn');
        closeBtn.addEventListener('click', () => this.hide());
        
        // 控制按钮
        const startBtn = this.container.querySelector('#floating-start-btn');
        const pauseBtn = this.container.querySelector('#floating-pause-btn');
        const stopBtn = this.container.querySelector('#floating-stop-btn');
        const nextStepBtn = this.container.querySelector('#floating-next-step-btn');
        
        startBtn.addEventListener('click', () => {
            console.log('悬浮UI: 点击启动按钮');
            this.sendMessageToBackground({
                command: 'start',
                isAutoApprove: true // 默认自动执行
            });
        });

        pauseBtn.addEventListener('click', () => {
            console.log('悬浮UI: 点击暂停/继续按钮');
            const command = pauseBtn.textContent === '暂停' ? 'pause' : 'resume';
            this.sendMessageToBackground({ command: command });
        });

        stopBtn.addEventListener('click', () => {
            console.log('悬浮UI: 点击停止按钮');
            this.sendMessageToBackground({ command: 'stop' });
        });

        nextStepBtn.addEventListener('click', () => {
            console.log('悬浮UI: 点击下一步按钮');
            this.sendMessageToBackground({ command: 'next_step' });
        });
        
        // 拖拽功能
        this.makeDraggable();
    }
    
    makeDraggable() {
        const header = this.container.querySelector('.floating-ui-header');
        let isDragging = false;
        let currentX;
        let currentY;
        let initialX;
        let initialY;
        let xOffset = 0;
        let yOffset = 0;
        
        header.addEventListener('mousedown', (e) => {
            if (e.target.tagName === 'BUTTON') return; // 不在按钮上拖拽
            
            initialX = e.clientX - xOffset;
            initialY = e.clientY - yOffset;
            
            if (e.target === header || header.contains(e.target)) {
                isDragging = true;
                header.style.cursor = 'grabbing';
            }
        });
        
        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                e.preventDefault();
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;
                
                xOffset = currentX;
                yOffset = currentY;
                
                this.container.style.transform = `translate(${currentX}px, ${currentY}px)`;
            }
        });
        
        document.addEventListener('mouseup', () => {
            initialX = currentX;
            initialY = currentY;
            isDragging = false;
            header.style.cursor = 'grab';
        });
    }
    
    show() {
        console.log('显示悬浮UI');
        this.isVisible = true;
        this.container.style.display = 'block';

        // 请求当前状态
        this.sendMessageToBackground({ command: 'getStatus' });
    }

    // 通过content script发送消息到background script
    sendMessageToBackground(message) {
        console.log('悬浮UI发送消息:', message);

        // 发送自定义事件到content script
        const event = new CustomEvent('floatingUIMessage', {
            detail: message
        });
        document.dispatchEvent(event);
    }

    // 接收来自content script的消息
    handleMessageFromBackground(message) {
        console.log('悬浮UI收到消息:', message);

        if (message.type === 'statusUpdate' || message.command === 'getStatus') {
            this.updateUI(message);
        }
    }
    
    hide() {
        this.isVisible = false;
        this.container.style.display = 'none';
    }
    
    toggleMinimize() {
        this.isMinimized = !this.isMinimized;
        const content = this.container.querySelector('#floating-ui-content');
        const minimizeBtn = this.container.querySelector('#floating-minimize-btn');
        
        if (this.isMinimized) {
            content.style.display = 'none';
            minimizeBtn.textContent = '+';
            minimizeBtn.title = '展开';
            this.container.classList.add('minimized');
        } else {
            content.style.display = 'block';
            minimizeBtn.textContent = '−';
            minimizeBtn.title = '最小化';
            this.container.classList.remove('minimized');
        }
    }
    
    updateUI(statusInfo) {
        if (!this.isVisible) return;
        
        this.currentStatusInfo = statusInfo;
        
        // 更新状态指示器
        this.updateStatusIndicator(statusInfo.state);
        
        // 更新变量表格
        this.updateVariables(statusInfo.variables);
        
        // 更新警告
        this.updateWarnings(statusInfo.warnings);
        
        // 更新错误
        this.updateError(statusInfo.errorInfo);
        
        // 更新完成状态
        this.updateCompletion(statusInfo.completionMessage);
        
        // 更新任务列表
        this.updateTaskList(statusInfo.script, statusInfo.currentStep, statusInfo.state);
        
        // 更新按钮状态
        this.updateButtons(statusInfo.state);
    }
    
    updateStatusIndicator(state) {
        const indicator = this.container.querySelector('#floating-status-indicator');
        indicator.className = 'floating-ui-status';
        
        let statusText = '';
        let statusClass = '';
        
        switch (state) {
            case 'running':
                statusText = '运行中';
                statusClass = 'status-running';
                break;
            case 'paused':
                statusText = '已暂停';
                statusClass = 'status-paused';
                break;
            case 'waiting_for_approval':
                statusText = '等待确认';
                statusClass = 'status-waiting';
                break;
            case 'waiting_for_user_input':
                statusText = '等待用户';
                statusClass = 'status-waiting-user';
                break;
            case 'idle':
                statusText = '空闲';
                statusClass = 'status-idle';
                break;
            case 'completed':
                statusText = '已完成';
                statusClass = 'status-completed';
                break;
            default:
                statusText = '未知';
                statusClass = 'status-unknown';
        }
        
        indicator.textContent = statusText;
        indicator.classList.add(statusClass);
    }

    updateVariables(variables) {
        const container = this.container.querySelector('#floating-variables-container');
        const tbody = this.container.querySelector('#floating-variables-body');

        if (!variables || Object.keys(variables).length === 0) {
            container.style.display = 'none';
            return;
        }

        container.style.display = 'block';
        tbody.innerHTML = '';

        Object.entries(variables).forEach(([name, info]) => {
            const row = document.createElement('tr');

            const nameCell = document.createElement('td');
            nameCell.textContent = name;

            const typeCell = document.createElement('td');
            typeCell.textContent = info.type || 'unknown';

            const valueCell = document.createElement('td');
            const valueStr = String(info.value);
            if (valueStr.length > 50) {
                valueCell.textContent = valueStr.substring(0, 47) + '...';
                valueCell.title = valueStr;
            } else {
                valueCell.textContent = valueStr;
            }

            row.appendChild(nameCell);
            row.appendChild(typeCell);
            row.appendChild(valueCell);
            tbody.appendChild(row);
        });
    }

    updateWarnings(warnings) {
        const container = this.container.querySelector('#floating-warnings-container');
        const list = this.container.querySelector('#floating-warnings-list');

        if (!warnings || warnings.length === 0) {
            container.style.display = 'none';
            return;
        }

        container.style.display = 'block';
        list.innerHTML = '';

        warnings.forEach(warning => {
            const li = document.createElement('li');
            li.innerHTML = `
                <div class="floating-warning-message">${warning.message}</div>
                ${warning.details ? `<div class="floating-warning-details">${warning.details}</div>` : ''}
            `;
            list.appendChild(li);
        });
    }

    updateError(errorInfo) {
        const container = this.container.querySelector('#floating-error-container');
        const messageDiv = this.container.querySelector('#floating-error-message');
        const detailsDiv = this.container.querySelector('#floating-error-details');

        if (!errorInfo) {
            container.style.display = 'none';
            return;
        }

        container.style.display = 'block';
        messageDiv.textContent = errorInfo.message || '未知错误';
        detailsDiv.textContent = errorInfo.details || '';
    }

    updateCompletion(completionMessage) {
        const container = this.container.querySelector('#floating-completion-container');
        const content = this.container.querySelector('#floating-completion-content');

        if (!completionMessage) {
            container.style.display = 'none';
            return;
        }

        container.style.display = 'block';
        content.textContent = completionMessage;
    }

    updateTaskList(script, currentStep, state) {
        const container = this.container.querySelector('#floating-task-list');

        if (!script || script.length === 0) {
            container.innerHTML = '<div class="floating-no-tasks">没有可用的任务</div>';
            return;
        }

        const ul = document.createElement('ul');
        ul.className = 'floating-task-items';

        script.forEach((step, index) => {
            const li = document.createElement('li');
            li.className = 'floating-task-item';

            // 确定任务状态
            let taskStatus = 'pending';
            let taskClass = '';

            if (index < currentStep) {
                taskStatus = 'completed';
                taskClass = 'completed';
            } else if (index === currentStep) {
                if (state === 'running') {
                    taskStatus = 'running';
                    taskClass = 'running';
                } else if (state === 'waiting_for_approval' || state === 'waiting_for_user_input') {
                    taskStatus = 'waiting';
                    taskClass = 'waiting';
                } else if (state === 'paused') {
                    taskStatus = 'paused';
                    taskClass = 'paused';
                }
            }

            li.classList.add(taskClass);

            const icon = this.icons[taskStatus] || this.icons.pending;
            const description = this.formatStepDetails(step);

            li.innerHTML = `
                <span class="floating-task-icon">${icon}</span>
                <span class="floating-task-description">${description}</span>
            `;

            ul.appendChild(li);
        });

        container.innerHTML = '';
        container.appendChild(ul);
    }

    updateButtons(state) {
        const startBtn = this.container.querySelector('#floating-start-btn');
        const pauseBtn = this.container.querySelector('#floating-pause-btn');
        const stopBtn = this.container.querySelector('#floating-stop-btn');
        const nextStepBtn = this.container.querySelector('#floating-next-step-btn');

        // 重置所有按钮状态
        startBtn.disabled = false;
        pauseBtn.disabled = true;
        stopBtn.disabled = true;
        nextStepBtn.disabled = true;
        pauseBtn.textContent = '暂停';

        switch (state) {
            case 'running':
                startBtn.disabled = true;
                pauseBtn.disabled = false;
                stopBtn.disabled = false;
                break;
            case 'paused':
                startBtn.disabled = true;
                pauseBtn.disabled = false;
                pauseBtn.textContent = '继续';
                stopBtn.disabled = false;
                break;
            case 'waiting_for_approval':
            case 'waiting_for_user_input':
                startBtn.disabled = true;
                stopBtn.disabled = false;
                nextStepBtn.disabled = false;
                break;
            case 'idle':
                // 默认状态，所有按钮都已正确设置
                break;
        }
    }

    formatStepDetails(step) {
        if (step.description) {
            return step.description;
        }

        // 如果没有description，则根据命令生成一个默认的
        let details = step.command || '';
        if (step.selector) {
            if (typeof step.selector === 'object') {
                details += ` by ${step.selector.by}: ${step.selector.value}`;
            } else {
                details += `: ${step.selector}`;
            }
        } else if (step.url) {
            details += `: ${step.url}`;
        } else if (step.variableName) {
            details += `: ${step.variableName}`;
        } else if (step.expression) {
            details += `: ${step.expression}`;
        } else if (step.condition) {
            details += `: ${step.condition}`;
        } else if (step.label) {
            details = `Label: ${step.label}`;
        }

        return details || '(未命名步骤)';
    }
}

// 导出到全局
window.FloatingUI = FloatingUI;

// 全局实例
window.approvalFloatingUI = null;
