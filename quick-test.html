<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>悬浮UI快速测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-card {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button.success {
            background: #28a745;
        }
        .test-button.danger {
            background: #dc3545;
        }
        .status-display {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .instructions {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #c3e6cb;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: #e9ecef;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <h1>悬浮UI快速测试页面</h1>
    
    <div class="instructions">
        <h3>🚀 快速测试步骤：</h3>
        <div class="step">
            <strong>步骤1：</strong> 确保扩展已安装并启用
        </div>
        <div class="step">
            <strong>步骤2：</strong> 点击下面的"显示悬浮UI"按钮
        </div>
        <div class="step">
            <strong>步骤3：</strong> 悬浮UI应该出现在页面右上角
        </div>
        <div class="step">
            <strong>步骤4：</strong> 测试悬浮UI上的各种按钮功能
        </div>
    </div>
    
    <div class="test-card">
        <h3>悬浮UI控制</h3>
        <button class="test-button success" onclick="showFloatingUI()">显示悬浮UI</button>
        <button class="test-button danger" onclick="hideFloatingUI()">隐藏悬浮UI</button>
        <button class="test-button" onclick="checkFloatingUIStatus()">检查状态</button>
    </div>
    
    <div class="test-card">
        <h3>脚本控制测试</h3>
        <button class="test-button success" onclick="testStartScript()">测试启动</button>
        <button class="test-button" onclick="testPauseScript()">测试暂停</button>
        <button class="test-button danger" onclick="testStopScript()">测试停止</button>
        <button class="test-button" onclick="testGetStatus()">获取状态</button>
    </div>
    
    <div class="test-card">
        <h3>消息传递测试</h3>
        <button class="test-button" onclick="testMessageBridge()">测试消息桥接</button>
        <button class="test-button" onclick="simulateStatusUpdate()">模拟状态更新</button>
        <button class="test-button" onclick="testDirectCall()">直接调用测试</button>
    </div>
    
    <div class="test-card">
        <h3>实时状态</h3>
        <div class="status-display" id="status-display">
            等待状态更新...
        </div>
        <button class="test-button" onclick="refreshStatus()">刷新状态</button>
        <button class="test-button" onclick="clearStatus()">清空显示</button>
    </div>
    
    <div class="test-card">
        <h3>测试元素</h3>
        <p>这些元素可以用于测试自动化脚本：</p>
        <button class="test-button" id="demo-btn-1">演示按钮1</button>
        <button class="test-button" id="demo-btn-2">演示按钮2</button>
        <input type="text" placeholder="测试输入框" style="margin: 8px; padding: 8px;">
        <select style="margin: 8px; padding: 8px;">
            <option>选项1</option>
            <option>选项2</option>
            <option>选项3</option>
        </select>
    </div>

    <script>
        let statusDisplay = document.getElementById('status-display');
        
        function updateStatus(message) {
            const timestamp = new Date().toLocaleTimeString();
            statusDisplay.textContent += `[${timestamp}] ${message}\n`;
            statusDisplay.scrollTop = statusDisplay.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }
        
        function clearStatus() {
            statusDisplay.textContent = '';
        }
        
        function refreshStatus() {
            updateStatus('=== 状态检查 ===');
            
            // 检查悬浮UI实例
            if (window.approvalFloatingUI) {
                updateStatus('✅ 悬浮UI实例存在');
                updateStatus(`   可见状态: ${window.approvalFloatingUI.isVisible}`);
                updateStatus(`   类型: ${window.approvalFloatingUI.constructor.name}`);
            } else {
                updateStatus('❌ 悬浮UI实例不存在');
            }
            
            // 检查消息监听器
            updateStatus('📡 消息监听器已设置');
            
            updateStatus('=== 检查完成 ===');
        }
        
        function showFloatingUI() {
            updateStatus('尝试显示悬浮UI...');
            
            // 方法1：通过popup消息
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({ command: 'show_floating_ui' }, (response) => {
                    if (chrome.runtime.lastError) {
                        updateStatus('❌ 显示失败: ' + chrome.runtime.lastError.message);
                    } else if (response && response.success) {
                        updateStatus('✅ 悬浮UI显示成功');
                    } else {
                        updateStatus('❌ 显示失败: ' + (response ? response.error : '未知错误'));
                    }
                });
            } else {
                updateStatus('❌ Chrome扩展API不可用');
            }
        }
        
        function hideFloatingUI() {
            updateStatus('尝试隐藏悬浮UI...');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({ command: 'hide_floating_ui' }, (response) => {
                    if (chrome.runtime.lastError) {
                        updateStatus('❌ 隐藏失败: ' + chrome.runtime.lastError.message);
                    } else if (response && response.success) {
                        updateStatus('✅ 悬浮UI隐藏成功');
                    } else {
                        updateStatus('❌ 隐藏失败: ' + (response ? response.error : '未知错误'));
                    }
                });
            } else {
                updateStatus('❌ Chrome扩展API不可用');
            }
        }
        
        function checkFloatingUIStatus() {
            updateStatus('检查悬浮UI状态...');
            refreshStatus();
        }
        
        function testStartScript() {
            updateStatus('测试启动脚本...');
            sendFloatingUIMessage({ command: 'start', isAutoApprove: true });
        }
        
        function testPauseScript() {
            updateStatus('测试暂停脚本...');
            sendFloatingUIMessage({ command: 'pause' });
        }
        
        function testStopScript() {
            updateStatus('测试停止脚本...');
            sendFloatingUIMessage({ command: 'stop' });
        }
        
        function testGetStatus() {
            updateStatus('测试获取状态...');
            sendFloatingUIMessage({ command: 'getStatus' });
        }
        
        function testMessageBridge() {
            updateStatus('测试消息桥接...');
            const testMessage = {
                command: 'test',
                data: 'bridge test',
                timestamp: Date.now()
            };
            sendFloatingUIMessage(testMessage);
        }
        
        function simulateStatusUpdate() {
            updateStatus('模拟状态更新...');
            const statusUpdate = {
                type: 'statusUpdate',
                state: 'running',
                currentStep: Math.floor(Math.random() * 5),
                variables: {
                    '测试变量': { type: 'string', value: '测试值' + Date.now() }
                }
            };
            
            const event = new CustomEvent('backgroundMessage', {
                detail: statusUpdate
            });
            document.dispatchEvent(event);
            updateStatus('已发送模拟状态更新');
        }
        
        function testDirectCall() {
            updateStatus('测试直接调用...');
            if (window.approvalFloatingUI) {
                if (window.approvalFloatingUI.isVisible) {
                    window.approvalFloatingUI.hide();
                    updateStatus('✅ 直接调用隐藏成功');
                } else {
                    window.approvalFloatingUI.show();
                    updateStatus('✅ 直接调用显示成功');
                }
            } else {
                updateStatus('❌ 悬浮UI实例不存在');
            }
        }
        
        function sendFloatingUIMessage(message) {
            const event = new CustomEvent('floatingUIMessage', {
                detail: message
            });
            document.dispatchEvent(event);
            updateStatus('已发送消息: ' + message.command);
        }
        
        // 监听消息事件
        document.addEventListener('floatingUIMessage', (event) => {
            updateStatus('📤 发出消息: ' + JSON.stringify(event.detail));
        });
        
        document.addEventListener('backgroundMessage', (event) => {
            updateStatus('📥 收到消息: ' + JSON.stringify(event.detail));
        });
        
        // 页面加载时初始化
        window.addEventListener('load', () => {
            updateStatus('页面已加载，开始初始化...');
            setTimeout(refreshStatus, 1000);
        });
        
        // 演示按钮功能
        document.getElementById('demo-btn-1').addEventListener('click', () => {
            updateStatus('演示按钮1被点击');
        });
        
        document.getElementById('demo-btn-2').addEventListener('click', () => {
            updateStatus('演示按钮2被点击');
        });
    </script>
</body>
</html>
