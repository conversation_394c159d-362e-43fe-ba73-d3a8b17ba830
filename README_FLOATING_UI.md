# 悬浮UI功能说明

## 概述

本次更新为approval_automation_extension项目添加了悬浮UI功能，允许用户在网页上直接查看和控制自动化脚本的执行状态，而不需要依赖浏览器扩展的popup窗口。

## 新增功能

### 1. 悬浮UI组件
- **位置**：默认显示在网页右上角
- **可拖拽**：用户可以拖拽悬浮UI到页面的任意位置
- **可最小化**：支持最小化/展开功能，节省屏幕空间
- **样式隔离**：使用CSS命名空间和!important规则，避免与网页样式冲突

### 2. 显示内容
悬浮UI包含以下信息模块：

#### 状态指示器
- 显示当前执行状态（运行中、暂停、等待确认等）
- 带有颜色编码和动画效果

#### 变量表格
- 显示所有当前变量的名称、类型和值
- 长文本自动截断并显示省略号
- 支持鼠标悬停查看完整内容

#### 警告信息
- 显示执行过程中的警告信息
- 包含警告消息和详细信息

#### 错误信息
- 显示执行错误的详细信息
- 包含错误消息和堆栈信息

#### 任务列表
- 显示脚本中的所有步骤
- 实时更新当前执行进度
- 不同状态用不同图标和颜色标识

#### 控制按钮
- 启动、暂停、停止、下一步等控制按钮
- 与popup中的按钮功能完全一致

### 3. 控制方式
用户可以通过以下方式控制悬浮UI：

#### 通过Popup控制
- 在扩展popup中新增了"显示悬浮UI"和"隐藏悬浮UI"按钮
- 点击即可控制悬浮UI的显示/隐藏

#### 直接操作
- 点击悬浮UI右上角的"×"按钮隐藏
- 点击"−"按钮最小化，点击"+"按钮展开
- 拖拽标题栏移动位置

## 技术实现

### 文件结构
```
scripts/
├── floating-ui.js      # 悬浮UI组件类
├── content.js          # 修改后的内容脚本（注入悬浮UI）
└── background.js       # 修改后的背景脚本（状态广播）

styles/
└── floating-ui.css     # 悬浮UI样式文件

popup/
├── popup.html          # 添加了悬浮UI控制按钮
├── popup.js            # 添加了按钮事件监听器
└── popup.css           # 添加了按钮样式

manifest.json           # 更新了web_accessible_resources
```

### 核心组件

#### FloatingUI类 (floating-ui.js)
- 负责创建和管理悬浮UI的DOM结构
- 处理用户交互（拖拽、最小化、控制按钮）
- 接收状态更新并刷新UI显示

#### 样式隔离 (floating-ui.css)
- 使用`.approval-floating-ui`命名空间
- 所有样式规则都添加了`!important`
- 确保不与网页原有样式冲突

#### 消息通信
- Background Script向所有标签页广播状态更新
- Content Script接收消息并更新悬浮UI
- Popup通过Background Script控制悬浮UI显示/隐藏

## 使用方法

### 1. 安装和启用
1. 确保扩展已正确安装并启用
2. 打开任意网页
3. 点击浏览器工具栏中的扩展图标

### 2. 显示悬浮UI
1. 在popup中点击"显示悬浮UI"按钮
2. 悬浮UI将出现在网页右上角
3. 可以拖拽到合适的位置

### 3. 使用悬浮UI
1. 查看当前执行状态和变量值
2. 使用控制按钮启动/暂停/停止脚本
3. 监控任务执行进度
4. 查看警告和错误信息

### 4. 隐藏悬浮UI
- 点击悬浮UI右上角的"×"按钮，或
- 在popup中点击"隐藏悬浮UI"按钮

## 测试

项目中包含了一个测试页面`test.html`，可以用来验证悬浮UI的功能：

1. 在浏览器中打开`test.html`
2. 按照页面上的说明操作
3. 测试悬浮UI的各项功能

## 兼容性

- 支持所有现代浏览器（Chrome、Firefox、Edge等）
- 与现有popup功能完全兼容
- 不影响原有的自动化脚本执行逻辑

## 注意事项

1. **权限要求**：悬浮UI需要在网页中注入脚本和样式，确保扩展有足够的权限
2. **性能影响**：悬浮UI会实时更新状态，在大量变量或长时间运行时可能有轻微性能影响
3. **样式冲突**：虽然已做样式隔离，但在某些特殊网页上仍可能出现样式问题
4. **安全限制**：在某些受保护的页面（如chrome://、about:等）无法显示悬浮UI

## 未来改进

1. 添加悬浮UI位置记忆功能
2. 支持更多自定义样式选项
3. 添加悬浮UI大小调整功能
4. 优化大数据量时的性能表现
