import logging
import uvicorn
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import JSONResponse

from scripts.ticket_parser import TicketParser
from scripts.rule_check import WorkTicketValidator

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="工作票自动校对服务",
    description="接收HTML格式的工作票，解析并根据规则进行校对，返回不合格及不规范项。",
    version="1.0.0",
)

@app.post("/validate", summary="校对工作票")
async def validate_ticket(request: Request):
    """
    接收HTML内容，进行解析和校对。

    - **request body**: 需要包含原始的HTML字符串。
    - **return**: 返回一个JSON对象，包含`unqualified`, `nonstandard`, 和 `manual_checks` 三个列表。
    """
    try:
        html_content = await request.body()
        html_content = html_content.decode('utf-8')

        if not html_content:
            raise HTTPException(status_code=400, detail="未提供HTML内容")

        # 1. 解析工作票
        parser = TicketParser()
        ticket_data, parsing_errors = parser.parse(html_content)
        
        if parsing_errors:
            logger.warning(f"解析HTML时出现错误: {parsing_errors}")
            raise HTTPException(status_code=400, detail={"message": "HTML解析失败", "errors": parsing_errors})
            
        logger.info("工作票解析完成")

        # 2. 规则检查
        validator = WorkTicketValidator(ticket_data)
        results = validator.validate()
        logger.info(f"校对完成: {results}")

        return JSONResponse(content=results)

    except Exception as e:
        logger.error(f"处理请求时发生错误: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {e}")

@app.get("/", summary="服务健康检查")
def read_root():
    """
    服务健康检查端点。
    """
    return {"status": "ok", "message": "欢迎使用工作票自动校对服务"}

if __name__ == "__main__":
    # 使用 uvicorn 启动服务
    # 在生产环境中，建议使用 gunicorn + uvicorn workers
    # uvicorn.run(app, host="127.0.0.1", port=2345)
    uvicorn.run("main:app", host="127.0.0.1", port=2345, reload=True)
